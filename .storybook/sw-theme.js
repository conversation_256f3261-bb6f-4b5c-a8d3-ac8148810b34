import { create } from '@storybook/theming/create';

const logo = "data:image/png;base64,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";

export default create({
  base: 'light',

  colorPrimary: 'hotpink',
  colorSecondary: '#2a2c44',

  // UI
  appBg: '#eaedf1',
  appContentBg: '#eaedf1',
  appBorderColor: 'rgba(0, 0, 0, 0.12)',
  appBorderRadius: 4,

  // Typography
  fontBase: 'Roboto,"Helvetica Neue",sans-serif',

  // Text colors
  textColor: 'rgba(0,0,0,.87)',
  textInverseColor: 'rgba(0,0,0,.87)',

  // Toolbar default and active colors
  barTextColor: 'rgba(41, 43, 67, 0.6)',
  barSelectedColor: 'rgba(41, 43, 67, 0.9)',
  barBg: '#fff',

  brandTitle: 'Swui Library',
  brandImage: logo,

});

// other styles are in manager-head.html
