import { Component, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  template: ''
})
export class NoopComponent {
}

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forRoot([
      { path: '', component: NoopComponent }
    ]),
  ],
  declarations: [NoopComponent]
})
export class RouteNoopModule {
}
