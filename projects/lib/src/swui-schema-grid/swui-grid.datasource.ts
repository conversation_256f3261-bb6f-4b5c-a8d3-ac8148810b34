import { DataSource } from '@angular/cdk/collections';
import { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Params } from '@angular/router';
import * as moment from 'moment';
import { BehaviorSubject, combineLatest, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, debounceTime, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { SwHubShortEntity } from '../services/sw-hub-entity/sw-hub-entity.model';
import { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';
import { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';
import { GridRequestData, SwuiGridDataService } from './services/grid-data.service';
import { LocalDataService } from './services/local-data.service';
import { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';

const FIELD_POSTFIX_DELIM = '__';

export class SwuiGridDataSource<T> extends DataSource<T> {

  set data( value: T[] | undefined ) {
    if (typeof value === 'undefined') {
      return;
    }
    this._data = value;
    this.dataService = new LocalDataService<T>();
    (this.dataService as LocalDataService<T>).localData = this._data;
  }

  get data(): T[] | undefined {
    return this._data;
  }

  sort?: MatSort;
  paginator?: MatPaginator;
  readonly total$ = new BehaviorSubject<number>(0);

  requestData?: GridRequestData;

  /**
   * required for not replacing URL queryParams for first request - when params are already in url but they also
   * adding via regular flow
   */
  private firstRequest = true;
  private useHubEntity = false;

  private _data: T[] | undefined;
  private filterData: any;
  private entity: SwHubShortEntity | null = null;

  private readonly data$ = new BehaviorSubject<T[]>([]);
  private readonly loadData$ = new Subject<void>();
  private readonly destroy$ = new Subject<void>();

  constructor(
    private dataService: SwuiGridDataService<T> | null,
    private readonly hubEntityService: SwHubEntityService,
    private readonly filterService: SwuiTopFilterDataService | null,
    private readonly urlHandler: SwuiGridUrlHandlerService
  ) {
    super();
  }

  get canUseEntitySelect(): boolean {
    return this.useHubEntity && !!this.hubEntityService;
  }

  get entity$(): Observable<any> {
    return this.canUseEntitySelect ? this.hubEntityService.entitySelected$ : of(null);
  }

  get filterEntity$(): Observable<any> {
    const filter$ = this.filterService ? this.filterService.appliedFilter : of(null);

    return combineLatest([filter$, this.entity$]);
  }

  initDatasource( useHubEntity: boolean ) {
    this.useHubEntity = useHubEntity;

    this.loadData$.pipe(
      debounceTime(250),
      switchMap(() => {
        if (typeof this.dataService === 'undefined' || this.dataService === null) {
          return of(new HttpResponse({
            body: undefined,
            headers: new HttpHeaders()
          }));
        }

        let params = this.getRequestParams();
        let requestData = this.requestData || {};

        if (this.entity && this.entity.path) {
          const path = this.entity.path.charAt(0) === ':' ? this.entity.path.substring(1) : this.entity.path;
          const type = this.entity.type;
          params = { ...params, path: this.entity.path };

          if (path) {
            requestData = { ...requestData, path, type };
          }
        }

        this.urlHandler.setParams(params, this.firstRequest ? 'merge' : '');
        let httpParams = new HttpParams({ fromObject: params });


        const request = this.dataService ? this.dataService.getGridData(httpParams, requestData) : throwError('');

        return request.pipe(
          take(1),
          catchError(() => of(new HttpResponse({
            body: undefined,
            headers: new HttpHeaders()
          }))),
          tap(() => this.firstRequest = false)
        );
      }),
      takeUntil(this.destroy$)
    ).subscribe(( { body, headers } ) => {
      this._data = body || undefined;
      this.data$.next(body || []);
      this.total$.next(Number(headers.get('x-paging-total') || '0'));
      if (!this.data?.length && this.paginator && this.paginator.pageIndex !== 0) {
        this.paginator.pageIndex = (this.paginator.pageIndex - 1);
        this.loadData();
      }
    });

    this.subscribeToFilter();
  }

  connect(): Observable<T[]> {
    return this.data$.asObservable();
  }

  disconnect() {
    this.data$.complete();
    this.destroy$.next();
    this.destroy$.complete();
  }

  addSort( sort: MatSort ): void {
    this.sort = sort;
    this.sort.sortChange.subscribe(() => this.loadData());
  }

  addPaginator( paginator: MatPaginator, blind = false ): void {
    this.paginator = paginator;
    this.paginator.page.subscribe(() => this.loadData());
    this.total$.asObservable().subscribe(( total ) => {
      if (!this.paginator) {
        return;
      }

      if (!blind) {
        this.paginator.length = total;
        return;
      }

      const previousLength = this.paginator.pageSize * this.paginator.pageIndex;
      const additionalItems = (this._data || []).length === this.paginator.pageSize ? 1 : 0;

      this.paginator.length = (this._data || []).length + additionalItems + previousLength;
    });
  }

  changePageSize( pageSize: number ) {
    if (this.paginator) {
      const oldPageSize = this.paginator.pageSize;
      const oldPageIndex = this.paginator.pageIndex;

      this.paginator.pageSize = pageSize;
      this.paginator.pageIndex = Math.floor((oldPageIndex * oldPageSize) / pageSize);

      if (this._data && this._data.length) {
        this.loadData();
      }
    }
  }

  loadData(): void {
    this.loadData$.next();
  }

  private subscribeToFilter() {
    if (this.filterService !== null) {
      this.filterService.displayedFilter
        .pipe(takeUntil(this.destroy$))
        .subscribe(filterData => {
          this.filterData = filterData;
        });

      this.filterEntity$
        .pipe(
          takeUntil(this.destroy$)
        )
        .subscribe(( [, entity] ) => {
          this.entity = entity;

          if (!this.firstRequest) {
            if (this.paginator) {
              this.paginator.pageIndex = 0;
            }
          }

          this.loadData();
        });

      return;
    }

    if (this.canUseEntitySelect) {
      this.hubEntityService.entitySelected$
        .pipe(takeUntil(this.destroy$))
        .subscribe(entity => {
          this.entity = entity;
          this.loadData();
        });
    }
  }

  private getRequestParams(): Params {
    let params: Params = {};

    // applying filter data
    if (this.filterData) {
      params = this.processFilterParams(this.filterData, params);
    }

    // applying paginator values
    if (this.paginator) {
      const { pageIndex, pageSize } = this.paginator;
      params['offset'] = (pageIndex * pageSize).toString();
      params['limit'] = pageSize.toString();
    } else if (this.data) {
      params['offset'] = 0;
    }

    // applying sorting
    if (this.sort) {
      const { active, direction } = this.sort;
      if (direction !== '') {
        params['sortOrder'] = direction.toUpperCase();
        params['sortBy'] = active;
      }
    }

    return params;
  }

  private processFilterParams( filterData: { [key: string]: any }, initialParams: Params, ): Params {
    return Object.keys(filterData)
      .filter(( key ) => {
        const value = filterData[key];
        return typeof value !== 'undefined' && value !== null && value !== '' && key !== 'undefined';
      })
      .reduce(( params, key ) => {
        let value = filterData[key];

        if (typeof value === 'object' && Object.keys(value).every(k => k.includes(FIELD_POSTFIX_DELIM))) {
          params = this.processFilterParams(value, params);
        } else {
          if (moment.isMoment(value)) {
            value = (value as moment.Moment).toISOString();
          }
          params[key] = value;
        }
        return params;
      }, initialParams);
  }
}
