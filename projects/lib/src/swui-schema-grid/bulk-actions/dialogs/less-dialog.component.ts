import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface LessDialogData {
  runAction: Function;
  declineAction: Function;
  available: any[];
  checked: any[];
}

@Component({
  selector: 'lib-swui-less-dialog',
  templateUrl: 'less-dialog.component.html'
})
export class LessDialogComponent {
  runAction: Function;
  declineAction: Function;
  available: any[] = [];
  checked: any[] = [];


  constructor(
    @Inject(MAT_DIALOG_DATA) data: LessDialogData,
  ) {
    this.available = data.available;
    this.checked = data.checked;
    this.runAction = data.runAction;
    this.declineAction = data.declineAction;
  }

}
