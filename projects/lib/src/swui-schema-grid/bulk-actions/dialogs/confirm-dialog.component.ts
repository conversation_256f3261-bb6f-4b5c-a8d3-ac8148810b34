import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface ConfirmDialogData {
  runAction: Function;
  declineAction: Function;
}

@Component({
  selector: 'lib-swui-confirm-dialog',
  templateUrl: 'confirm-dialog.component.html'
})
export class ConfirmDialogComponent {

  runAction: Function;
  declineAction: Function;

  constructor(
    @Inject(MAT_DIALOG_DATA) data: ConfirmDialogData,
  ) {
    this.declineAction = data.declineAction;
    this.runAction = data.runAction;

  }
}
