import { Component, Input } from '@angular/core';
import { BulkAction } from './bulk-actions.model';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { NoDataDialogComponent } from './dialogs/no-data-dialog.component';
import { LessDialogComponent } from './dialogs/less-dialog.component';
import { ConfirmDialogComponent } from './dialogs/confirm-dialog.component';

@Component({
  selector: 'lib-swui-bulk-actions',
  templateUrl: './bulk-actions.component.html',
  styleUrls: ['./bulk-actions.component.scss']
})
export class BulkActionsComponent {
  @Input() rows: any[] = []; // selected rows
  @Input() actions: BulkAction[] = [];

  dialogRef: MatDialogRef<any> | undefined;

  availableRows: any[] | undefined;  // rows which has passed canActivateFn
  currentAction: BulkAction | undefined;

  constructor( private dialog: MatDialog ) {
  }

  prepareAction( event: Event, action: BulkAction ): void {
    event.preventDefault();

    this.availableRows = this.rows.filter(action.canActivateFn);
    this.currentAction = action;

    if (!this.availableRows.length) {
      this.dialogRef = this.dialog.open(NoDataDialogComponent, {
        width: '400px',
        data: {
          currentAction: this.currentAction,
          declineAction: this.declineAction.bind(this),
        }
      });
    } else if (this.availableRows.length < this.rows.length) {
      this.dialogRef = this.dialog.open(LessDialogComponent, {
        width: '400px',
        data: {
          available: this.availableRows,
          checked: this.rows,
          declineAction: this.declineAction.bind(this),
          runAction: this.runAction.bind(this),
        }
      });
    } else if (!!this.currentAction.dialog) {
      const { componentRef, config = {} } = this.currentAction.dialog;
      this.dialogRef = this.dialog.open(componentRef, {
        ...config,
        data: {
          rows: this.availableRows,
          declineAction: this.declineAction.bind(this)
        }
      });
    } else {
      this.dialogRef = this.dialog.open(ConfirmDialogComponent, {
        width: '400px',
        data: {
          runAction: this.runAction.bind(this),
          declineAction: this.declineAction.bind(this),
        }
      });
    }
  }

  runAction(): void {
    if (this.currentAction) {
      this.currentAction.fn(this.availableRows);
    }
    this.declineAction();
  }

  declineAction(): void {
    this.currentAction = undefined;
    this.availableRows = undefined;

    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  emptyAction( event: Event ): void {
    event.preventDefault();
    event.stopImmediatePropagation();
  }
}
