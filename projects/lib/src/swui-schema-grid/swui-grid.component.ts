import { SelectionModel } from '@angular/cdk/collections';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  InjectionToken,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, SortDirection } from '@angular/material/sort';
import { BehaviorSubject, merge, Subject } from 'rxjs';
import { debounceTime, skip, switchMap, take, takeUntil } from 'rxjs/operators';
import { AppSettings, DEFAULT_PAGE_SIZE } from '../services/settings/app-settings';
import { SettingsService } from '../services/settings/settings.service';
import { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';
import { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';
import { SchemaVisibilityData } from './columns-management/columns-management.model';
import { WidgetRegistry } from './registry/registry';
import { RowAction } from './row-actions/row-actions.component';
import { SwuiGridDataService } from './services/grid-data.service';
import { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';
import { SwuiGridDataSource } from './swui-grid.datasource';
import { SwuiGridSchemaField } from './swui-grid.model';
import { SwDexieService } from '../services/sw-dexie/sw-dexie.service';


export interface SelectionTransformer {
  transform( data: any ): any;
}

export interface SelectionRowAvailable {
  available( data: any ): boolean;
}

export const SWUI_GRID_SELECTION_TRANSFORMER_TOKEN = new InjectionToken<SelectionTransformer>('grid-selection-transformer');
export const SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN = new InjectionToken<SelectionTransformer>('grid-selection-row-available');

@Component({
  selector: 'lib-swui-grid',
  templateUrl: './swui-grid.component.html',
  styleUrls: ['./swui-grid.component.scss'],
})
export class SwuiGridComponent<T, S = T> implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  get schema(): SwuiGridSchemaField[] {
    return this._schema;
  }

  @Input() set schema( value: SwuiGridSchemaField[] ) {
    this._schema = [...value];

    this.addActionsToSchema();
    this.updateDisplayedColumns();
  }

  get data(): T[] | undefined {
    return this._data;
  }

  @Input() set data( value: T[] | undefined ) {
    if (typeof value === 'undefined') {
      return;
    }
    this._data = value;
  }

  @Input() selected: S[] = [];
  @Input() rowActions: RowAction[] = [];
  @Input() rowActionsColumnTitle = 'COMPONENTS.GRID.GRID_ROW_ACTIONS';
  @Input() rowActionsMenuIcon = 'more_horiz';
  @Input() bulkActions: RowAction[] = [];
  @Input() ignorePlainLink = false;

  @Input() pagination = true;
  @Input() stickyHeader = false;
  @Input() columnsManagement = false;
  @Input() gridId = 'default-grid-id';
  @Input() queryParamsAffectsPageSize = false;
  @Input() ignoreQueryParams = false;
  @Input() useHubEntity = false;
  @Input() bulkSelectionOnly = false; // for case when we need checkboxes for rows but not need bulk actions in general
  @Input() footer = false;
  @Input() blindPaginator = false;
  @Input() disableRefreshAction = false;
  @Input() showTotalItems = true;
  @Input() totalItemsTitle = 'COMPONENTS.GRID.ITEMS_FOUND';
  @Input() sortActive?: string;
  @Input() sortDirection: SortDirection = '';

  @Input() savedFilteredPageName?: string;
  @Input() savedFilteredPageParams?: any;
  @Input() loading = false; // for case with data

  /**
   * if pageSize is undefined:
   * - and we have settings service, then value will be taken from settings;
   * - and there are no settings service, then default value will be set;
   * if pageSize value was taken from input, then it becomes strict and can't be changed from settings
   */
  @Input() pageSize: number | undefined;

  @Output() widgetActionEmitted = new EventEmitter<any>();

  @ViewChild(MatPaginator) paginator?: MatPaginator;
  @ViewChild(MatSort) sort?: MatSort;

  // schema items which will be rendered in mat-table
  columnDefSchema: SwuiGridSchemaField[] = [];
  displayedColumns: string[] = [];
  dataSource!: SwuiGridDataSource<T>;
  selection!: SelectionModel<S>;
  loading$ = new BehaviorSubject(true);
  total = 0;

  bulkActionsColumnName = 'bulk-actions-column';
  rowActionsColumnName = 'row-actions-column';

  private _schema: SwuiGridSchemaField[] = [];
  private _data: T[] | undefined;
  private readonly destroyed$ = new Subject<void>();

  private selectionPageCheckedOutdated = true;
  private selectionPageChecked = false;

  constructor(
    readonly registry: WidgetRegistry,
    private readonly cdr: ChangeDetectorRef,
    private readonly urlHandler: SwuiGridUrlHandlerService,
    private readonly element: ElementRef,
    private readonly dexieService: SwDexieService,
    @Optional() private readonly filter: SwuiTopFilterDataService,
    @Optional() private readonly hubEntityService: SwHubEntityService,
    @Optional() private readonly settings: SettingsService,
    @Optional() @Inject(SwuiGridDataService) private service: SwuiGridDataService<T>,
    @Optional() @Inject(SWUI_GRID_SELECTION_TRANSFORMER_TOKEN) private selectionTransformer: SelectionTransformer,
    @Optional() @Inject(SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN) private selectionRowAvailable: SelectionRowAvailable,
  ) {
    this.dataSource = new SwuiGridDataSource<T>(this.service, this.hubEntityService, this.filter, this.urlHandler);
  }

  get isEmpty(): boolean | undefined {
    return !this.dataSource.data || this.dataSource.data.length === 0;
  }

  ngOnInit(): void {
    this.dataSource.initDatasource(this.useHubEntity);
    this.initSelection();
    this.addActionsToSchema();
    this.buildColumnDefSchema();
    this.updateDisplayedColumns();
    this.configureUrlHandler();
    this.dataSource.total$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(total => {
      this.total = total;
    });
  }

  ngAfterViewInit(): void {
    this.setupPageSize();
    this.addPaginatorToDataSource();
    this.watchForLoading();
    this.useQueryParamsData();
    this.setFilterState();
    this.attachSort();

    if (!this.filter) {
      this.dataSource.loadData();
    }
    this.hideTotal();
    this.cdr.detectChanges();
  }

  ngOnChanges( changes: SimpleChanges ): void {
    if ('data' in changes) {
      this.dataSource.data = changes['data'].currentValue;
      if (!changes['data'].isFirstChange()) {
        if (this.paginator) {
          this.paginator.firstPage();
        }
        this.dataSource.loadData();
      }
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  isAllSelected() {
    if (this.selectionPageCheckedOutdated && this.dataSource.data) {
      this.selectionPageChecked = this.dataSource.data
        .filter(( row ) => this.isRowAvailable(row))
        .every(( row ) => this.selection.isSelected(this.getSelectionRow(row)));
      this.selectionPageCheckedOutdated = false;
    }
    return this.selectionPageChecked;
  }

  // adds or removes to selection
  // Please note that we can have multiple data arrays in selection model due to pagination of data
  masterToggle() {
    if (this.dataSource.data) {
      const allSelected = this.isAllSelected();
      this.dataSource.data
        .filter(( row ) => this.isRowAvailable(row))
        .forEach(( row ) => allSelected
          ? this.selection.deselect(this.getSelectionRow(row))
          : this.selection.select(this.getSelectionRow(row))
        );
    }
  }

  columnsChanged( visibilityData: SchemaVisibilityData ) {
    this._schema.forEach(item => {
      if (item.field in visibilityData) {
        item.isListVisible = visibilityData[item.field].isListVisible;
      }
    });
    this.updateDisplayedColumns();
  }

  onPageClick() {
    this.showLoading();
  }

  getSelectionRow( row: T ): S {
    let transformFn;
    if (!!this.selectionTransformer) {
      transformFn = this.selectionTransformer.transform;
    }
    return transformFn ? transformFn(row) : row;
  }

  isRowAvailable( row: T ): boolean {
    let availableFn;
    if (!!this.selectionRowAvailable) {
      availableFn = this.selectionRowAvailable.available;
    }
    return availableFn ? availableFn(row) : true;
  }

  refreshData() {
    this.dataSource.loadData();
  }

  private attachSort() {
    if (this.sort) {
      this.dataSource.addSort(this.sort);
      this.sort.sortChange
        .pipe(takeUntil(this.destroyed$))
        .subscribe(() => this.showLoading());
    }
  }

  private addPaginatorToDataSource() {
    if (this.paginator && this.pageSize) {
      this.paginator.pageSize = this.pageSize;
      this.dataSource.addPaginator(this.paginator, this.blindPaginator);
    }
  }

  /**
   * Adds internal actions columns
   * Needs to work in Columns Visibility with it
   */
  private addActionsToSchema() {
    // bulk action column is first by default
    if (this.bulkActions && this.bulkActions.length) {
      const bulkActionsSchemaItem: SwuiGridSchemaField = {
        field: this.bulkActionsColumnName,
        title: 'Bulk Actions',
        isList: true,
        isListVisible: true,
        type: 'internal'
      };
      this.schema.unshift(bulkActionsSchemaItem);
    }
    // row actions column is last by default
    if (this.rowActions && this.rowActions.length) {
      const rowActionsSchemaItem: SwuiGridSchemaField = {
        field: this.rowActionsColumnName,
        title: this.rowActionsColumnTitle,
        isList: true,
        isListVisible: true,
        type: 'internal'
      };
      this.schema.push(rowActionsSchemaItem);
    }
  }

  /**
   * Updates schema which is used in *ngFor for building column definitions in mat-table template
   * We need to remove internal items such as row/bulk actions, because they are already presents in mat-table template
   */
  private buildColumnDefSchema() {
    this.columnDefSchema = this._schema
      .filter(( { field } ) => field !== this.rowActionsColumnName && field !== this.bulkActionsColumnName);
  }

  /**
   * Updates column names which will be shown in mat-table
   */
  private updateDisplayedColumns() {
    this.displayedColumns = this._schema
      .filter(( { isListVisible } ) => isListVisible !== false)
      .map(( { field } ) => field);
  }

  private watchForLoading() {
    const skipCount = this._data ? 0 : 1; // hotfix to prevent first empty data loading
    this.dataSource.connect()
      .pipe(
        skip(skipCount),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.hideLoading());

    if (this.filter) {
      this.filter.appliedFilter
        .pipe(takeUntil(this.destroyed$))
        .subscribe(() => this.showLoading());
    }

    if (this.hubEntityService) {
      this.hubEntityService.entitySelected$
        .pipe(takeUntil(this.destroyed$))
        .subscribe(() => this.showLoading());
    }
  }

  private showLoading() {
    this.loading$.next(true);
  }

  private hideLoading() {
    this.loading$.next(false);
    this.hideTotal();
  }

  private useQueryParamsData() {
    if (this.ignoreQueryParams) {
      return;
    }

    if (this.paginator) {
      if (this.queryParamsAffectsPageSize) {
        this.pageSize = this.urlHandler.fetchPageSize(this.pageSize);
        this.paginator.pageSize = this.pageSize;
      }
      this.urlHandler.setParamsToPaginator(this.paginator);
    }

    if (this.sort) {
      this.urlHandler.setParamsToSort(this.sort);
    }

    if (this.filter) {
      const filterParams = this.urlHandler.getFilterQueryParams();
      if (filterParams.hasOwnProperty('path')) {
        delete filterParams['path'];
      }

      if (Object.keys(filterParams).length) {
        this.dexieService.getFilterState(this.savedFilteredPageName)
          .then(filterState => {
            const state = Object.keys(filterState).reduce((res: any, key) => {
              res[key] = null;

              return res;
            }, {});

            this.filter.submitFilter({ ...(state || {}), ...(filterParams || {}) });
          })
          .catch(() => this.filter.submitFilter(filterParams));

        return;
      }

      if (!this.savedFilteredPageName) {
        this.filter.submitFilter(filterParams);
      } else {
        this.dexieService.getFilterState(this.savedFilteredPageName)
          .then(filterState => {
            this.filter.submitFilter({ ...(filterState || {}), ...(this.savedFilteredPageParams || {}), ...(filterParams || {}) });
          })
          .catch(() => this.filter.submitFilter(filterParams));
      }
    }
  }

  private setFilterState() {
    this.filter?.appliedFilter
      .pipe(
        take(1),
        switchMap(() => this.filter.filterFormState),
        debounceTime(500)
      )
      .subscribe(filter => {
        if (this.savedFilteredPageName) {
          this.dexieService.putFilterState(this.savedFilteredPageName, filter)
            .catch(( e ) => console.log(e));
        }
      });
  }

  private configureUrlHandler() {
    this.urlHandler.setAllowQueryParamsUpdate(!this.ignoreQueryParams);
  }

  private initSelection() {
    this.selection = new SelectionModel<S>(true, this.selected);
    merge(this.selection.changed.asObservable(), this.dataSource.connect())
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => this.selectionPageCheckedOutdated = true);
  }

  private setupPageSize() {
    if (typeof this.pageSize === 'undefined') {
      if (this.settings) {
        this.subscribeForSettings();
      } else {
        this.pageSize = DEFAULT_PAGE_SIZE;
      }
    }
  }

  /**
   * Subscribes for settings changes and updates specific fields
   */
  private subscribeForSettings() {
    this.settings.appSettings$
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( { pageSize }: AppSettings ) => {
        if (this.paginator) {
          if (pageSize) {
            this.overwritePageSize(pageSize);
          } else {
            this.addPaginatorToDataSource();
          }
        }
      });
  }

  /**
   * Changes page size for existing grid with different pageSize value
   * (e.g. if we changed pageSize in settings dialog from 10 to 20)
   * @param pageSize
   */
  private overwritePageSize( pageSize: number ) {
    if (this.pageSize !== pageSize) {
      this.pageSize = pageSize;

      if (this.paginator) {
        this.showLoading();
        this.dataSource.changePageSize(pageSize);
      }
    }
  }

  private hideTotal() {
    if (!this.blindPaginator || !this.paginator || !this.dataSource.data) {
      return;
    }
    const el = this.element.nativeElement.getElementsByClassName('mat-paginator-range-label');
    if (el && el[0]) {
      if (this.dataSource.data.length) {
        const firstIndex = this.paginator.pageIndex * this.paginator.pageSize;
        el[0].innerText = `${firstIndex + 1} - ${firstIndex + this.dataSource.data.length}`;
      } else {
        el[0].innerText = '0 - 0';
      }
    }
  }
}
