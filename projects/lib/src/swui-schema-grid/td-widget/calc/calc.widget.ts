import { Component, Inject } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';
import { Truncate } from '../../pipes/truncate.interface';

export interface SwuiCalcTdWidgetSchema {
  td?: {
    titleFn?: ( row: any, schema: SwuiCalcTdWidgetSchema ) => string;
    classFn?: ( row: any, schema: SwuiCalcTdWidgetSchema ) => string;
    sanitizeValue?: boolean;
    useTranslate?: boolean;
    truncate: Truncate | undefined;
  };
}

@Component({
  selector: 'lib-swui-td-calc-widget',
  templateUrl: './calc.widget.html'
})
export class SwuiTdCalcWidgetComponent {
  readonly value: string;
  readonly row: any;
  truncate: Truncate | undefined;
  titleFn: any;
  readonly classObj: any;
  readonly useTranslate: boolean;

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { value, schema, row }: SwuiGridWidgetConfig<SwuiCalcTdWidgetSchema>,
    sanitizer: DomSanitizer
  ) {
    this.value = value;
    this.row = row;
    this.truncate = schema.td && schema.td.truncate ? schema.td.truncate : undefined;
    const titleFn = schema.td?.titleFn;
    const classFn = schema.td?.classFn;

    this.titleFn = (titleFn && titleFn(this.row, schema)) || this.value;
    this.classObj = classFn && classFn(this.row, schema);
    this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;

    // TODO: unsafe operation
    if (schema.td && schema.td.sanitizeValue) {
      this.titleFn = sanitizer.bypassSecurityTrustHtml(this.titleFn);
    }
  }
}
