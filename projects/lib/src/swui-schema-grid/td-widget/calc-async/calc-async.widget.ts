import { Component, Inject } from '@angular/core';
import { Observable } from 'rxjs';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiAsyncCalcTdWidgetSchema {
  td?: {
    source: Observable<any>;
    titleFn: ( data: any, row: any, schema: any ) => string;
    classFn: ( data: any, row: any, schema: any ) => string;
    useTranslate?: boolean;
  };
}

@Component({
  selector: 'lib-swui-td-calc-async-widget',
  templateUrl: './calc-async.widget.html'
})
export class SwuiTdCalcAsyncWidgetComponent {
  title = '';
  class = '';
  readonly useTranslate: boolean;
  loading = true;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { schema, row }: SwuiGridWidgetConfig<SwuiAsyncCalcTdWidgetSchema> ) {
    const source$ = schema.td?.source as Observable<any>;
    const titleFn = schema.td?.titleFn;
    const classFn = schema.td?.classFn;

    if (titleFn && classFn) {
      source$.subscribe(data => {
        this.title = titleFn(data, row, schema);
        this.class = classFn(data, row, schema);
        this.loading = false;
      });
    }

    this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;
  }
}
