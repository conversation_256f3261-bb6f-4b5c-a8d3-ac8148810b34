import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

// tslint:disable:no-bitwise
function hashCode( str: string ): number | undefined {
  if (!str) {
    return;
  }
  let hash = 0;
  if (str.length === 0) {
    return hash;
  }
  let i;
  let chr;
  for (i = 0; i < str.length; i++) {
    chr = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + chr;
    hash |= 0; // Convert to 32bit integer
  }
  return hash;
}

function scaleHash( hash: number, min: number, max: number ): number {
  const maxHash = 2147483647;
  const minHash = 0;
  if (hash < 0) {
    hash *= -1;
  }
  let percent = (hash - minHash) / (maxHash - minHash);
  return Math.floor(percent * (max - min) + min);
}

@Component({
  selector: 'lib-swui-td-colorful-labels-widget',
  templateUrl: './colorful-labels.widget.html'
})
export class SwuiTdColorfulLabelsWidgetComponent {
  colorfulClass: string;
  value: string;

  private tones = ['-300', '-400', '', '-600', '-700', '-800'];
  protected palettes = ['pink', 'violet', 'purple', 'indigo', 'blue', 'teal', 'green', 'orange', 'brown', 'grey', 'slate'];

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { field, value, row }: SwuiGridWidgetConfig<{}> ) {
    this.value = value;
    if (row.hasOwnProperty(field)) {
      const value1 = row[field];
      if (value1) {
        this.value = value1;
      }
    }
    this.colorfulClass = this.getRandomClassByValue(this.value) || '';
  }

  getRandomClassByValue( value: string ): string | undefined {
    const classes = this.getAvailableClasses();
    const hash = hashCode(value);
    if (hash) {
      const index = scaleHash(hash, 0, classes.length);
      return classes[index];
    }
  }

  private getAvailableClasses(): string[] {
    const result: string[] = [];
    this.palettes.forEach(palette => {
      this.tones.forEach(tone => {
        result.push('bg-' + palette + tone);
      });
    });
    return result;
  }
}
