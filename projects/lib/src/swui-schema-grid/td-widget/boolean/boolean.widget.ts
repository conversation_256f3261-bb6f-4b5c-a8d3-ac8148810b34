import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiBooleanTdWidgetSchema {
  td?: {
    titleFn?: ( row: any ) => string;
  };
}

@Component({
  selector: 'lib-swui-td-boolean-widget',
  templateUrl: './boolean.widget.html'
})
export class SwuiTdBooleanWidgetComponent {
  value: any;
  tooltipText: string | undefined;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { value, row, schema }: SwuiGridWidgetConfig<SwuiBooleanTdWidgetSchema> ) {
    this.value = value;
    if (schema.td && schema.td.titleFn) {
      this.tooltipText = schema.td.titleFn(row);
    }
  }
}
