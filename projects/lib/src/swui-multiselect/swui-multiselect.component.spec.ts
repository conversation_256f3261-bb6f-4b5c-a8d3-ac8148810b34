import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormControl } from '@angular/forms';
import { DebugElement } from '@angular/core';

import { SwuiMultiselectComponent } from './swui-multiselect.component';
import { MULTISELECT_MODULES } from './swui-multiselect.module';
import { SwuiSelectOption } from '../swui-select/swui-select.interface';


describe('SwuiMultiselectComponent', () => {
  let component: SwuiMultiselectComponent;
  let fixture: ComponentFixture<SwuiMultiselectComponent>;
  let testOptions: SwuiSelectOption[] = [];
  let testValue: string[];
  let selectControl: FormControl;
  let host: DebugElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        ...MULTISELECT_MODULES
      ],
      declarations: [SwuiMultiselectComponent]
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiMultiselectComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testOptions = [
      {id: '1', text: 'Solo Option1'},
      {id: '2', text: 'Test Option2'},
      {id: '3', text: 'Option3', disabled: true},
      {id: '4', text: 'Test Option4'},
      {id: '5', text: 'Option5'},
    ];
    testValue = ['1', '2'];
    component.data = testOptions;
    selectControl = component.selectControl;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testValue;
    expect(component.value).toEqual(['1', '2']);
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBeTruthy();
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBeTruthy();
    expect(selectControl.disabled).toBeTruthy();

    component.disabled = false;
    expect(component.disabled).toBeFalsy();
    expect(selectControl.disabled).toBeFalsy();
  });

  it('should set empty', () => {
    expect(component.empty).toBeTruthy();

    component.value = testValue;
    expect(component.empty).toBeFalsy();
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-multiselect');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should shouldLabelFloat ', () => {
    expect(component.shouldLabelFloat).toBeFalsy();

    component.disabled = true;
    expect(component.shouldLabelFloat).toBeFalsy();

    component.disabled = false;
    component.value = testValue;
    expect(component.shouldLabelFloat).toBeTruthy();
  });

  it('should call onChange when apply', () => {
    spyOn(component, 'onChange');
    component.onApply(['1']);
    fixture.detectChanges();
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should set onChange when apply', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.onApply(testValue);
    expect(test).toBe(true);
  });

  it('should setDisabledState', () => {
    component.setDisabledState(true);
    expect(component.disabled).toBeTruthy();
    expect(selectControl.disabled).toBeTruthy();

    component.setDisabledState(false);
    expect(component.disabled).toBeFalsy();
    expect(selectControl.disabled).toBeFalsy();
  });

  it('should write value', () => {
    component.writeValue(testValue);
    expect(component.value).toEqual(testValue);
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should not set valueAccessor if form control', () => {
    (fixture.componentInstance as any).ngControl = new FormControl(testValue);
    expect(component.ngControl.valueAccessor).toBeUndefined();
  });

  it('should set showSearch', () => {
    expect(component.showSearch).toBeFalsy();

    component.showSearch = true;
    expect(component.showSearch).toBeTruthy();
  });

  it('should set searchPlaceholder', () => {
    component.searchPlaceholder = 'Atata';
    expect(component.searchPlaceholder).toBe('Atata');
  });

  it('should close open menu', () => {
    if (component.selectRef) {
      component.selectRef.openMenu();
      expect(component.selectRef.menuOpen).toBe(true);

      component.onCancel();
      expect(component.selectRef.menuOpen).toBe(false);
    }
  });

  it('should set select value on apply', () => {
    component.value = testValue;
    expect(component.selectControl.value).toBeTruthy();
  });

});


function createFakeEvent(type: string) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent(node: Node | Window, type: string) {
  node.dispatchEvent(createFakeEvent(type));
}
