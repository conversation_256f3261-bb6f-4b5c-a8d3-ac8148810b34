import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { FormControl } from '@angular/forms';
import { filter, map, takeUntil } from 'rxjs/operators';

import { SwuiSelectOption } from '../swui-select/swui-select.interface';


@Component({
  selector: 'lib-swui-menu-select',
  templateUrl: './swui-menu-select.component.html',
  styleUrls: ['./swui-menu-select.component.scss'],
})
export class SwuiMenuSelectComponent implements OnInit {
  @Input() searchPlaceholder = 'COMPONENTS.MENU_SELECT.search';
  @Input() title = '';
  @Input() showSearch = false;
  @Input() selectAll = false;

  @Input()
  set data( val: SwuiSelectOption[] ) {
    const value = val ? val : [];
    this._data$.next(value);
    this._filtered = value.map(( item: SwuiSelectOption ) => item.id);
  }

  @Input()
  set selected( val: string[] ) {
    const processed = Array.isArray(val) ? val : [];
    this._sourceSelected = processed;
    this._selected$.next(processed);
  }

  @Output() applyData = new EventEmitter<string[]>();
  @Output() cancelClick = new EventEmitter();

  searchControl = new FormControl();
  processedData: SwuiSelectOption[] = [];

  private _sourceSelected: string[] = [];
  private _data$ = new BehaviorSubject<SwuiSelectOption[]>([]);
  private _selected$ = new BehaviorSubject<string[]>([]);
  private _filtered: string[] = [];
  private readonly _destroyed$ = new Subject<void>();

  constructor() {
    combineLatest([this._data$, this._selected$])
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( [data, selected] ) => {
        const selectedSet = new Set<string>(selected);
        this.processedData = data.reduce<SwuiSelectOption[]>(( result, option ) => {
          option.data = {
            checked: selectedSet.has(option.id)
          };
          return [...result, ...[option]];
        }, []);
      });
  }

  ngOnInit() {
    this.initFilter();
  }

  clear( event: Event ) {
    event.preventDefault();
    this._selected$.next([]);
  }

  onSelectAll( event: Event ) {
    event.preventDefault();
    this._selected$.next(this.processedData.filter(( { disabled } ) => !disabled).map(( { id } ) => id));
  }

  cancel( event: Event ) {
    event.preventDefault();
    this._selected$.next(this._sourceSelected);
    this.cancelClick.emit();
  }

  apply( event: Event ) {
    event.preventDefault();
    const resultData = this.processedData.filter(( el: SwuiSelectOption ) => el.data.checked);
    const output = resultData ? resultData.map(( el: SwuiSelectOption ) => el.id) : [];

    this.applyData.emit(output);
  }

  isFiltered( id: string ): boolean {
    return this._filtered.indexOf(id) > -1;
  }

  get selectedLength(): number {
    return this.processedData.filter(( el: SwuiSelectOption ) => el.data.checked).length;
  }

  prevent( event: MouseEvent ) {
    event.stopPropagation();
  }

  private initFilter() {
    this.searchControl.valueChanges
      .pipe(
        filter(() => this.showSearch),
        map(( searchString: string ) => searchString.toLowerCase()),
        takeUntil(this._destroyed$)
      )
      .subscribe(( searchString: string ) => {
        const processed = this.processedData.filter(( option: SwuiSelectOption ) => {
          return option.text && option.text.toLowerCase().indexOf(searchString) > -1;
        });
        this._filtered = processed ? processed.map(( item: SwuiSelectOption ) => item.id) : [];
      });
  }
}
