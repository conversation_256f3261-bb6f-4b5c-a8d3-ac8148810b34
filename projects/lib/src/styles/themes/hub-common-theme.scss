@use '~@angular/material' as mat;
@use 'sass:math';
@use '~@angular/material/core/typography/typography-utils';
@include mat.core();
@import 'global';


$hub-primary-palette: (
  50 : #e3edf9,
  100 : #b9d2f1,
  200 : #8ab4e7,
  300 : #5b95dd,
  400 : #377fd6,
  500 : #1468cf,
  600 : #1260ca,
  700 : #0e55c3,
  800 : #0b4bbd,
  900 : #063ab2,
  A100 : #dce5ff,
  A200 : #a9beff,
  A400 : #7698ff,
  A700 : #5d84ff,
  contrast: (
    50 : #000000,
    100 : #000000,
    200 : #000000,
    300 : #000000,
    400 : #ffffff,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #000000,
    A200 : #000000,
    A400 : #000000,
    A700 : #000000,
  )
);
$hub-typography: mat.define-typography-config(

  $display-4:     mat.define-typography-level(112px, 112px, 300, $letter-spacing: -0.05em),
  $display-3:     mat.define-typography-level(56px, 56px, 400, $letter-spacing: -0.02em),
  $display-2:     mat.define-typography-level(45px, 48px, 400, $letter-spacing: -0.005em),
  $display-1:     mat.define-typography-level(34px, 40px, 400),
  $headline:      mat.define-typography-level(26px, 32px, 400),

  $title:         mat.define-typography-level(22px, 31px, 500), //

  $subheading-2:  mat.define-typography-level(18px, 28px, 400),
  $subheading-1:  mat.define-typography-level(17px, 24px, 400),

  $body-2:        mat.define-typography-level(16px, 24px, 400), //
  $body-1:        mat.define-typography-level(16px, 26px, 400), //

  $caption:       mat.define-typography-level(14px, 22px, 400), //

  $button:        mat.define-typography-level(16px, 23px, 500), //
    // Line-height must be unit-less fraction of the font-size.
  $input:         mat.define-typography-level(inherit, 1.125, 400)
);

$hub-primary: mat.define-palette($hub-primary-palette);
$hub-accent: mat.define-palette($hub-primary-palette);
$hub-warn: mat.define-palette(mat.$red-palette);

$hub-theme: mat.define-light-theme((
  color: (
    primary: $hub-primary,
    accent: $hub-accent,
    warn: $hub-warn
  ),
  typography: $hub-typography,
));

$custom-background-color: #eaedf1;
$background: map-get($hub-theme, background);
$background: map_merge($background, (background: $custom-background-color));

@include mat.all-component-themes($hub-theme);

// Overrides
$outline-dedupe: 0;

@mixin _label-floating($font-scale, $infix-padding, $infix-margin-top) {
  transform: translateY(-$infix-margin-top - $infix-padding + $outline-dedupe) scale($font-scale);
  width: math.div(100%, $font-scale) + $outline-dedupe;

  $outline-dedupe: $outline-dedupe + 0.00001 !global;
}

@mixin mat-form-field-outline-typography($config) {
  $line-height: typography-utils.line-height($config, input);
  $subscript-font-scale: 0.75;
  $infix-padding: 0.5em;
  $infix-margin-top: $subscript-font-scale * typography-utils.private-coerce-unitless-to-em($line-height);
  $subscript-margin-top:  math.div(0.5em, $subscript-font-scale);
  $wrapper-padding-bottom: ($subscript-margin-top + $line-height) * $subscript-font-scale;
  $outline-appearance-label-offset: -0.25em;

  .mat-form-field-appearance-outline {
    .mat-form-field-infix {
      padding: $infix-padding 0 $infix-padding * 1.2 0;
    }

    .mat-form-field-wrapper {
      padding-bottom: $wrapper-padding-bottom;
    }

    .mat-form-field-label {
      top: $infix-margin-top + $infix-padding;
      margin-top: $outline-appearance-label-offset;
    }

    &.mat-form-field-can-float {
      &.mat-form-field-should-float .mat-form-field-label,
      .mat-input-server:focus + .mat-form-field-label-wrapper .mat-form-field-label {
        @include _label-floating(
            $subscript-font-scale, $infix-padding + $outline-appearance-label-offset, $infix-margin-top);
      }
      .mat-input-server[label]:not(:label-shown) + .mat-form-field-label-wrapper .mat-form-field-label {
        @include _label-floating(
            $subscript-font-scale, $infix-padding + $outline-appearance-label-offset, $infix-margin-top);
      }
    }
  }
};
@mixin mat-table-theme($theme) {
  $background: map-get($theme, background);
  $foreground: map-get($theme, foreground);

  .mat-table {
    background: mat.get-color-from-palette($background, 'card');
  }

  .mat-table thead, .mat-table tbody, .mat-table tfoot,
  mat-header-row, mat-row, mat-footer-row,
  [mat-header-row], [mat-row], [mat-footer-row],
  .mat-table-sticky {
    background: inherit;
  }

  mat-row, mat-header-row, mat-footer-row,
  th.mat-header-cell, td.mat-cell, td.mat-footer-cell {
    border-bottom-color: mat.get-color-from-palette($foreground, divider);
  }

  .mat-header-cell {
    color: mat.get-color-from-palette($foreground, text);
  }

  .mat-cell, .mat-footer-cell {
    color: mat.get-color-from-palette($foreground, text);
  }
};
@mixin swui-calendar-theme($theme) {
  $primary: map-get($theme, primary);

  .swui-calendar {
    &__inner {
      &--selected {
        background-color: mat.get-color-from-palette($primary);
        &:hover{
          background-color: mat.get-color-from-palette($primary);
        }
      }
    }
  }
}
@mixin swui-snackbar-theme($theme) {
  $warn: map-get($theme, warn);

  .mat-snack-bar-container {
    width: 350px;
    margin: 16px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
    display: inline-table !important;
  }

  .swui-snackbar-success {
    background: rgba(67, 166, 70, 0.9);
  }

  .swui-snackbar-error {
    background-color: rgba(242, 59, 47, 0.9);
  }

  .swui-snackbar-warning {
    background-color: mat.get-color-from-palette($warn);
  }
}
@mixin swui-custom-period-theme($theme) {
  $primary: map-get($theme, primary);

  .swui-calendar {
    &__inner {
      &--selected {
        background-color: mat.get-color-from-palette($primary);
        &:hover{
          background-color: mat.get-color-from-palette($primary);
        }
      }
    }
  }
}
@mixin color-primary($theme) {
  $primary: map-get($theme, primary);
  .color-primary {
    color: mat.get-color-from-palette($primary);
  }
}
@mixin swAdditionalBgColors() {
  $color-palette-list: (
    'red': mat.$red-palette,
    'pink': mat.$pink-palette,
    'purple': mat.$purple-palette,
    'deep-purple': mat.$deep-purple-palette,
    'indigo': mat.$indigo-palette,
    'blue': mat.$blue-palette,
    'light-blue': mat.$light-blue-palette,
    'cyan': mat.$cyan-palette,
    'teal': mat.$teal-palette,
    'green': mat.$green-palette,
    'light-green': mat.$light-green-palette,
    'lime': mat.$lime-palette,
    'yellow': mat.$yellow-palette,
    'amber': mat.$amber-palette,
    'orange': mat.$orange-palette,
    'deep-orange': mat.$deep-orange-palette,
    'brown': mat.$brown-palette,
    'grey': mat.$grey-palette,
    'blue-grey': mat.$blue-grey-palette,
  ) !default;

  @each $type, $color in $color-palette-list {
    .sw-bg-#{$type} {
      background-color: mat.get-color-from-palette(mat.define-palette($color)) !important;
    }
    .sw-color-#{$type} {
      color: mat.get-color-from-palette(mat.define-palette($color)) !important;
    }
    .sw-border-color-#{$type} {
      border-color: mat.get-color-from-palette(mat.define-palette($color)) !important;
    }
  }
}



@include mat-form-field-outline-typography($hub-typography);
@include mat-table-theme($hub-theme);
@include swui-calendar-theme($hub-theme);
@include swui-snackbar-theme($hub-theme);
@include swui-custom-period-theme($hub-theme);
@include color-primary($hub-theme);
@include swAdditionalBgColors();
@import "mat-components-override";
@import "helpers";

