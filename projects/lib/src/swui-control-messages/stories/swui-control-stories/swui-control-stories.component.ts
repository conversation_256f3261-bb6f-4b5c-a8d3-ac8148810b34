import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';

export interface ErrorMessage {
  [key: string]: any;
}

@Component({
  selector: 'lib-swui-control-stories',
  templateUrl: './swui-control-stories.component.html',
})
export class SwuiControlStoriesComponent {
  form: FormGroup;

  messageErrors: ErrorMessage = {
    required: 'field is required',
    maxlength: `Maximum length is ${3}`,
  };

  get testControl(): FormControl {
    return this.form.get('testControl') as FormControl;
  }

  get testControlEmpty(): FormControl {
    return this.form.get('testControlEmpty') as FormControl;
  }

  get testControlForced(): FormControl {
    return this.form.get('testControlForced') as FormControl;
  }

  constructor(private fb: FormBuilder ) {
    this.form = this.fb.group({
      testControl: ['0', Validators.compose([
        Validators.required,
        Validators.maxLength(3)
      ])],
      testControlEmpty: [null, Validators.compose([
        Validators.required,
        Validators.maxLength(3)
      ])],
      testControlForced: [null, Validators.compose([
        Validators.required,
        Validators.maxLength(3)
      ])],
    });
  }
}
