import { ChangeDetectionStrategy, Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import { ControlValueAccessor, FormArray, FormControl, FormGroup, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

interface SelectOptionItem {
  id: string;
  text: string;
  disabled?: boolean;
}

export interface SwuiSequenceMapOptions {
  addButtonLabel?: string;
  removeButtonLabel?: string;
  sourcePlaceholder?: string;
  targetPlaceholder?: string;
  sourceOptions?: SelectOptionItem[];
  targetOptions?: SelectOptionItem[];
  title?: string;
}

@Component({
  selector: 'lib-swui-input-sequence-map',
  templateUrl: './swui-input-sequence-map.component.html',
  styleUrls: ['./swui-input-sequence-map.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwuiInputSequenceMapComponent),
      multi: true
    }
  ]
})
export class SwuiInputSequenceMapComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() id?: string;
  @Input() readonly = false;
  @Input() submitted = false;
  @Input() options?: SwuiSequenceMapOptions;

  formArray = new FormArray([]);

  private readonly destroyed$ = new Subject<void>();

  ngOnInit(): void {
    this.writeValue({});

    this.formArray.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {
      const data: Record<string, string> = {};
      this.formArray.controls.forEach(control => {
        const group = control as FormGroup;
        const source = group.get('source')?.value?.trim();
        const target = group.get('target')?.value?.trim();
        if (source && target) {
          data[source] = target;
        }
      });
      this.onChange(data);
      this.onTouched();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  // ControlValueAccessor implementation
  writeValue(value: Record<string, string>): void {
    while (this.formArray.length !== 0) {
      this.formArray.removeAt(0);
    }
    Object.entries(value || {}).forEach(([source, target]) => {
      this.addPair(source, target);
    });
    if (this.formArray.length === 0) {
      this.addPair();
    }
  }

  registerOnChange(fn: (value: Record<string, string>) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.readonly = isDisabled;
    if (isDisabled) {
      this.formArray.disable();
    } else {
      this.formArray.enable();
    }
  }

  addPair(source = '', target = ''): void {
    this.formArray.push(new FormGroup({
      source: new FormControl(source, [Validators.required]),
      target: new FormControl(target, [Validators.required])
    }));
  }

  removePair(index: number): void {
    if (this.formArray.length > 1) {
      this.formArray.removeAt(index);
    }
  }

  getSourceControl(index: number): FormControl {
    return this.formArray.at(index)?.get('source') as FormControl;
  }

  getTargetControl(index: number): FormControl {
    return this.formArray.at(index)?.get('target') as FormControl;
  }

  hasSourceError(index: number): boolean {
    const control = this.getSourceControl(index);
    return Boolean(control?.invalid && (control?.dirty || control?.touched || this.submitted));
  }

  hasTargetError(index: number): boolean {
    const control = this.getSourceControl(index);
    return Boolean(control?.invalid && (control?.dirty || control?.touched || this.submitted));
  }

  getSourceErrorMessage(index: number): string {
    const control = this.getSourceControl(index);
    if (control?.hasError('required')) {
      return 'required';
    }
    return '';
  }

  getTargetErrorMessage(index: number): string {
    const control = this.getTargetControl(index);
    if (control?.hasError('required')) {
      return 'required';
    }
    return '';
  }

  private onChange = (_value: Record<string, string>) => { };

  private onTouched = () => { };
}
