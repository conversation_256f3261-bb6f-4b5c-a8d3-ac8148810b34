import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  // tslint:disable-next-line:directive-selector
  selector: 'input[select-on-click], textarea[select-on-click]'
})
export class SelectOnClickDirective {

  constructor( elementRef: ElementRef ) {
    elementRef.nativeElement.style.cursor = 'text';
  }

  @HostListener('click', ['$event']) onClick( { target }: any ) {
    target.select();
  }
}
