import { Directive, ElementRef, Output, EventEmitter, HostListener } from '@angular/core';

@Directive({
  selector: '[libSwuiClickOutside]'
})
export class SwuiClickOutsideDirective {
  @Output() isOutside = new EventEmitter();

  constructor(private elementRef: ElementRef) {
  }
  @HostListener('document:click', ['$event.target'])
  public onClick(targetElement: HTMLElement) {
    if (this.elementRef) {
      const clickedInside = this.elementRef.nativeElement.contains(targetElement);
      if (!clickedInside) {
          this.isOutside.emit(true);
      }
    }
  }
}
