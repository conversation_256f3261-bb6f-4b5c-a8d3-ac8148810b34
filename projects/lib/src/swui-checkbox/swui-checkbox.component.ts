import { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'lib-swui-checkbox',
  template: `
    <label class="checkbox-inline" [ngClass]="cssClass" [class.disabled]="isDisabled">
      <div class="checker">
        <span [class.checked]="isChecked">
          <input
            class="bo-checkbox__input"
            type="checkbox"
            [disabled]="isDisabled"
            [checked]="isChecked"
            (change)="handleChange()">
        </span>
      </div>
      <ng-content></ng-content>
    </label>
  `,
  styles: [],
  providers: [
    {provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => SwuiCheckboxComponent), multi: true},
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SwuiCheckboxComponent implements ControlValueAccessor {

  @Input() cssClass: string | undefined;

  public isChecked = false;
  public isDisabled = false;

  constructor(private cdr: ChangeDetectorRef) {
  }

  public onChange = (_: boolean) => {
  };
  public onTouched = () => {
  };

  writeValue(value: boolean) {
    if (value) {
      this.isChecked = value;
      this.cdr.detectChanges();
    }
  }

  registerOnChange(fn: (_: any) => void) {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void) {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  public handleChange() {
    this.isChecked = !this.isChecked;
    this.cdr.detectChanges();
    this.onChange(this.isChecked);
  }
}
