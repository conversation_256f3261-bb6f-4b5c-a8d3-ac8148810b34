import { TestBed } from '@angular/core/testing';

import { SwBrowserTitleService } from './sw-browser-title.service';
import { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';

describe('BrowserTitleService', () => {
  let service: SwBrowserTitleService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterTestingModule
      ],
      providers: [
        SwBrowserTitleService,
        SwHubConfigService
      ]
    });
    service = TestBed.inject(SwBrowserTitleService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
