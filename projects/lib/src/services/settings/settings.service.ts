import { AppSettings, DEFAULT_PAGE_SIZE } from './app-settings';
import { Resolve } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { Injectable } from '@angular/core';
import * as moment from 'moment';
import 'moment-timezone';

export const DEFAULT_SETTINGS: AppSettings = {
  pageSize: DEFAULT_PAGE_SIZE,
  currencyFormat: window.navigator.language,
  dateFormat: 'DD.MM.YYYY',
  timeFormat: 'HH:mm:ss',
  timezoneName: moment.tz.guess()
};

@Injectable()
export class SettingsService implements Resolve<AppSettings> {
  settings: BehaviorSubject<AppSettings> = new BehaviorSubject<AppSettings>(DEFAULT_SETTINGS);

  use( settings: AppSettings ) {
    this.settings.next(settings);
  }

  get appSettings$() {
    return this.settings.asObservable();
  }

  get appSettings() {
    return this.settings.value;
  }

  resolve(): AppSettings {
    return this.settings.value;
  }
}
