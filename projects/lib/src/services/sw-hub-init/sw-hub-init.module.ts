import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { jwtOptionsFactory, SwHubAuthModule } from '../sw-hub-auth/sw-hub-auth.module';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { LanguageInterceptor } from './http-interceptors/language-interceptor';
import { HeadersInterceptor } from './http-interceptors/headers-interceptor';
import { UnauthorizedInterceptor } from './http-interceptors/unauthorized-interceptor';
import { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';
import { JWT_OPTIONS, JwtHelperService, JwtInterceptor } from '@auth0/angular-jwt';
import { SwHubAuthGuard } from '../sw-hub-auth/sw-hub-auth.guard';
import { SWUI_HUB_AUTH_CONFIG } from '../sw-hub-auth/sw-hub-auth.token';
import { SettingsService } from '../settings/settings.service';
import { SwuiSidebarModule } from '../../swui-sidebar/swui-sidebar.module';
import { Observable } from 'rxjs';
import { SwHubInitService } from './sw-hub-init.service';
import { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';
import { SwHubMessageModuleConfig } from './sw-hub-init.model';
import { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';

export class SwHubTranslateLoader implements TranslateLoader {

  constructor( private readonly http: HttpClient ) {
  }

  getTranslation( lang: string ): Observable<any> {
    return this.http.get(`locale/${lang}.json?=${(Date.now())}`);
  }
}

@NgModule({
  imports: [
    CommonModule,
    SwHubAuthModule,
    HttpClientModule,
    TranslateModule.forRoot(),
    SwuiSidebarModule
  ],
  providers: []
})
export class SwHubInitModule {

  static forRoot( config: SwHubMessageModuleConfig ): ModuleWithProviders<SwHubInitModule> {
    return {
      ngModule: SwHubInitModule,
      providers: [
        SwHubAuthService,
        JwtHelperService,
        SwHubAuthGuard,
        SwHubInitService,
        TranslateService,
        SettingsService,
        SwHubEntityService,
        {
          provide: SWUI_HUB_AUTH_CONFIG,
          useValue: config.auth || {}
        },
        {
          provide: JWT_OPTIONS,
          useFactory: jwtOptionsFactory,
          deps: [SWUI_HUB_AUTH_CONFIG, SwHubAuthService]
        },
        {
          provide: SWUI_HUB_MESSAGE_CONFIG,
          useValue: config
        },
        {
          provide: TranslateLoader,
          useClass: SwHubTranslateLoader,
          deps: [HttpClient]
        },
        { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: HeadersInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: LanguageInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: UnauthorizedInterceptor, multi: true },
      ]
    };
  }
}
