import { Inject, Injectable, OnDestroy } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, fromEvent, merge, Observable, Subject } from 'rxjs';
import { filter, map, share, take, takeUntil, tap, throttleTime } from 'rxjs/operators';
import { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';
import { AppSettings } from '../settings/app-settings';
import { SettingsService } from '../settings/settings.service';
import { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';
import { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';
import { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';
import {
  BridgeLoadedMessageBody, HubMessage, isHubMessage, NavigationHistoryBody, SwHubMessageModuleConfig,
  TokenExpiredMessageBody, TYPES
} from './sw-hub-init.model';
import { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';

@Injectable()
export class SwHubInitService implements OnDestroy {
  readonly message$: Observable<HubMessage>;
  readonly navigation$ = new BehaviorSubject<NavigationHistoryBody | null>(null);

  private readonly destroyed$ = new Subject<void>();
  private readonly ready$ = new Subject<Window>();
  private ready = false;
  private win?: Window;

  constructor(
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly authService: SwHubAuthService,
    private readonly settingsService: SettingsService,
    private readonly entityService: SwHubEntityService,
    private readonly configService: SwHubConfigService,
    private readonly sidebarService: SwuiSidebarService,
    @Inject(SWUI_HUB_MESSAGE_CONFIG) private readonly config: SwHubMessageModuleConfig
  ) {
    const source = fromEvent<MessageEvent>(window, 'message').pipe(
      filter(event => isHubMessage(event)),
      map<MessageEvent, HubMessage>(( { data } ) => data),
      filter(( { target } ) => target === config.name || target === '*'),
      tap(data => console.log('[hub] getMessage', data)),
      share()
    );
    this.message$ = source.pipe(
      filter(( { type } ) => ![TYPES.BRIDGE_LOADED, TYPES.LOGIN, TYPES.LOGOUT].includes(type))
    );

    source.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( { type, body } ) => {
      switch (type) {
        case TYPES.BRIDGE_LOADED:
          const { accessToken, lang, settings, navigation, entityId, grantedPermissions, twoFactor } = body as BridgeLoadedMessageBody;
          this.ready = true;
          if (this.win) {
            this.ready$.next(this.win);
          }
          this.authService.setToken(accessToken, grantedPermissions, twoFactor);
          if (lang) {
            this.translate.use(lang);
          }
          if (settings) {
            this.settingsService.use(settings);
          }
          if (navigation) {
            this.navigation$.next(navigation);
          }
          if (entityId) {
            this.entityService.useByPath(entityId);
          }
          break;
        case TYPES.TOKEN:
        case TYPES.LOGIN:
          this.authService.setToken(body.accessToken, body.grantedPermissions, body.twoFactor);
          break;
        case TYPES.LOGOUT:
          this.authService.logout();
          if (this.configService.loginUrl) {
            location.href = this.configService.loginUrl;
          }
          break;
        case TYPES.LOCALE_CHANGED:
          this.translate.use(body);
          break;
        case TYPES.APP_SETTINGS_CHANGED:
          this.settingsService.use(body);
          break;
        case TYPES.ENTITY_ID_CHANGED:
          this.entityService.use(body);
          break;
        case TYPES.SIDEBAR_COLLAPSED:
          this.sidebarService.isCollapsed.next(body === true);
          break;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  init() {
    this.translate.addLangs(this.config.langs.map(( { id } ) => id));
    if (this.config.defaultLang) {
      this.translate.setDefaultLang(this.config.defaultLang);
    }

    this.router.events.pipe(
      filter( event => event instanceof NavigationEnd ),
      map( event => event as NavigationEnd ),
      map( event => {
        const foundException = this.config?.lastLocationExceptions?.find( exception => event.url.startsWith( exception.url ) );
        return foundException ? foundException.replaceTo : event.url;
      } ),
      takeUntil( this.destroyed$ )
    ).subscribe( url => {
      const body: NavigationHistoryBody = { hub: this.config.name, url: url };
      this.postMessage( TYPES.LOCATION_CHANGED, body );
    } );

    if (this.configService.bridge) {
      this.create(this.configService.bridge);
    }
    this.postMessage(TYPES.HUB_LOADED);
  }

  initInactivityWatcher(): void {
    merge(
      fromEvent(window, 'mousemove'),
      fromEvent(window, 'keypress'),
    ).pipe(
      throttleTime(10000),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.postMessage(TYPES.USER_ACTIVITY, new Date());
    });
    this.postMessage(TYPES.USER_ACTIVITY, new Date());
  }

  sendLogin( accessToken: string, grantedPermissions: string, twoFactor: boolean ) {
    this.postMessage(TYPES.LOGIN, {
      accessToken,
      grantedPermissions,
      twoFactor
    });
  }

  sendLogout() {
    this.postMessage(TYPES.LOGOUT);
  }

  sendLocale( lang: string ) {
    this.postMessage(TYPES.LOCALE_CHANGED, lang);
  }

  sendAppSettings( settings: AppSettings ) {
    this.postMessage(TYPES.APP_SETTINGS_CHANGED, settings);
  }

  sendEntityId( entityId: string ) {
    this.postMessage(TYPES.ENTITY_ID_CHANGED, entityId);
  }

  sendTokenExpired( body: TokenExpiredMessageBody ) {
    this.postMessage(TYPES.TOKEN_EXPIRED, body);
  }

  sendUnknownUserLocation( url: string ) {
    this.postMessage(TYPES.UNKNOWN_LOCATION, url);
  }

  private postMessage( type: string, body?: any ) {
    const message: HubMessage = {
      target: 'bridge',
      initiator: this.config.name,
      type,
      body
    };
    if (this.win && this.ready) {
      this.win.postMessage(message, '*');
    } else {
      console.log('[hub] queue message', type);
      this.ready$.pipe(take(1)).subscribe(win => {
        console.log('[hub] post queued message', type);
        win.postMessage(message, '*');
      });
    }
  }

  private create( url: string ): void {
    const el = document.createElement('iframe');
    el.src = url;
    el.style.display = 'none';
    const frame = document.body.appendChild(el);
    console.log('[hub] frame created');
    frame.onload = () => {
      if (frame.contentWindow) {
        console.log('[hub] frame loaded');
        this.win = frame.contentWindow;
        if (this.ready) {
          this.ready$.next(this.win);
        }
      }
    };
  }
}
