import { TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ActivatedRoute } from '@angular/router';
import { SwHubEntityService } from './sw-hub-entity.service';


describe('SwHubEntityService', () => {
  let service: SwHubEntityService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        HttpClientTestingModule,
      ],
      providers: [
        SwHubEntityService,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {}
            }
          }
        }
      ],
    });
    service = TestBed.inject(SwHubEntityService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
