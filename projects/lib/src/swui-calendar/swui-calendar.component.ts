import { Component, forwardRef, HostBinding, HostListener, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import * as moment from 'moment';

function toMoment( value: moment.Moment | string | undefined | null ): moment.Moment | null {
  if (typeof value === 'undefined' || value === null) {
    return null;
  }
  if (moment.isMoment(value)) {
    return value;
  }
  const date = moment.parseZone(value);
  if (date.isValid()) {
    return date;
  }
  return null;
}

@Component({
  selector: 'lib-swui-calendar',
  templateUrl: './swui-calendar.component.html',
  styleUrls: ['./swui-calendar.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwuiCalendarComponent),
      multi: true
    },
  ],
})

export class SwuiCalendarComponent implements ControlValueAccessor {
  @Input() minDate?: moment.Moment | string;
  @Input() maxDate?: moment.Moment | string;

  @Input()
  set timeZone( val: string | undefined ) {
    if (!val) {
      return;
    }
    this._timeZone = val;
    const current = moment.tz(val);
    if (current.isValid()) {
      this.currentDate = current.clone();
      this.setMonth(this.currentDate);
    }
  }

  get timeZone(): string | undefined {
    return this._timeZone;
  }

  weekDayNames = moment.weekdaysShort();
  monthNames = moment.monthsShort();
  currentDate: moment.Moment = moment.utc();
  currentMonth: moment.Moment[][] = [];
  selectedDate: moment.Moment | undefined;
  isCalendarDisabled = false;

  @HostBinding('attr.tabindex')
  public tabindex = 0;

  onChange: ( _: any ) => void = (() => {
  });

  private _timeZone?: string;

  constructor() {
    this.setMonth(this.currentDate);
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  onTouched: any = () => {
  };

  writeValue( value: moment.Moment | string | undefined | null ) {
    const date = toMoment(value);
    if (date && !this.isDayDisabled(date)) {
      this.selectedDate = date.clone();
      if (!this.selectedDate.isSame(this.currentDate, 'month')) {
        this.setMonth(this.selectedDate.clone());
      }
    }
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isCalendarDisabled = disabled;
  }

  nextMonth( event: MouseEvent ) {
    event.preventDefault();
    this.setMonth(this.currentDate.add(1, 'months'));
  }

  prevMonth( event: MouseEvent ) {
    event.preventDefault();
    this.setMonth(this.currentDate.add(-1, 'months'));
  }

  selectDay( day: moment.Moment | null, event?: any ): void {
    if (event) {
      event.preventDefault();
    }
    if (day && !this.isDayDisabled(day)) {
      this.selectedDate = day.clone();
      if (this.selectedDate && !this.selectedDate.isSame(this.currentDate, 'month')) {
        this.setMonth(this.selectedDate.clone());
      }
      this.onChange(this.selectedDate);
    }
  }

  isDayToday( day: moment.Moment ): boolean {
    return day ? day.isSame(this.timeZone ? moment.tz(this.timeZone) : moment.utc(), 'date') : false;
  }

  isDaySelected( day: moment.Moment ): boolean {
    return day && this.selectedDate ? day.isSame(this.selectedDate, 'date') : false;
  }

  isDayDisabled( day: moment.Moment ): boolean {
    const minDate = this.minDate ? toMoment(this.minDate) : undefined;
    const maxDate = this.maxDate ? toMoment(this.maxDate) : undefined;
    return (day && minDate && day.isBefore(minDate, 'date')) ||
      (day && maxDate && day.isAfter(maxDate, 'date')) ||
      false;
  }

  private setMonth( date: moment.Moment ) {
    const firstDay = date.clone().startOf('month');
    const lastDay = date.clone().endOf('month');
    const result: moment.Moment[][] = [];
    while (firstDay.date() <= lastDay.date()) {
      if (!result.length || !firstDay.day()) {
        result.push([]);
      }
      result[result.length - 1][firstDay.day()] = firstDay.clone();
      if (firstDay.date() === lastDay.date()) {
        break;
      } else {
        firstDay.add(1, 'days');
      }
    }
    this.currentDate = date.clone().startOf('day');
    this.currentMonth = result;
  }
}
