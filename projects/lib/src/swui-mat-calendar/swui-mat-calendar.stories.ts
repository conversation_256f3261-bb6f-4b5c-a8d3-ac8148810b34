import { moduleMetadata, storiesOf } from '@storybook/angular';
import { MatCardModule } from '@angular/material/card';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import * as moment from 'moment';

import { MAT_CALENDAR_MODULES, SwuiMatCalendarModule } from './swui-mat-calendar.module';
import { SwuiMatCalendarComponent } from './swui-mat-calendar.component';


const template = `
  <mat-card style="margin: 32px">
    <lib-swui-mat-calendar
      [disabled]="disabled"
      [timeZone]="timeZone"
      [maxDate]="maxDate"
      [minDate]="minDate"
      [(ngModel)]="value">
    </lib-swui-mat-calendar>
  </mat-card>
`;

storiesOf('Date/MatCalendar', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiMatCalendarModule,
        MAT_CALENDAR_MODULES,
        MatCardModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiMatCalendarComponent,
    template,
    props: {
    },
  }))
  .add('timeZone +13', () => ({
    component: SwuiMatCalendarComponent,
    template,
    props: {
      timeZone: 'Pacific/Tongatapu',
    }
  }))
  .add('value 2020-06-30 00:00 timezone -1', () => ({
    component: SwuiMatCalendarComponent,
    template,
    props: {
      timeZone: 'America/Godthab',
      value: '2020-06-30T00:00:00.000Z'
    }
  }))
  .add('minDate today -2', () => ({
    component: SwuiMatCalendarComponent,
    template,
    props: {
      minDate: moment().add(-2, 'days').toISOString(),
    }
  }))
  .add('maxDate today +2', () => ({
    component: SwuiMatCalendarComponent,
    template,
    props: {
      maxDate: moment().add(2, 'days').toISOString()
    }
  }))
  .add('disabled', () => ({
    component: SwuiMatCalendarComponent,
    template,
    props: {
      disabled: true
    }
  }));

