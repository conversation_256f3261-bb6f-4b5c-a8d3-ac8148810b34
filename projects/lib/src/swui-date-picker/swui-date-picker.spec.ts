import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiDatePickerComponent } from './swui-date-picker.component';
import { DATE_PICKER_MODULES } from './swui-date-picker.module';
import { SwuiDatePickerConfig, SwuiDatePickerConfigModel } from './swui-date-picker-config.model';


describe('SwuiDatePickerComponent', () => {
  let component: SwuiDatePickerComponent;
  let fixture: ComponentFixture<SwuiDatePickerComponent>;
  let host: DebugElement;
  let testIsoString: string;
  let testConfig: SwuiDatePickerConfig;
  let defaultConfig: SwuiDatePickerConfig;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiDatePickerComponent],
      imports: [
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        ...DATE_PICKER_MODULES
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiDatePickerComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testIsoString = '2020-07-10T00:00:00.000Z';
    testConfig = {
      dateFormat: 'MM:DD',
      timeFormat: 'hh:mm z',
      timeZone: 'Asia/Taipei',
      timePicker: true
    };
    defaultConfig = {
      dateFormat: 'DD.MM.YYYY',
      timeFormat: 'HH:mm:ss',
      timePicker: false,
      timeDisableLevel: undefined,
      timeZone: undefined
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testIsoString;
    expect(component.value).toBe(testIsoString);
    expect(component.inputControl.value).toBe('10.07.2020');
  });

  it('should writeValue', () => {
    component.writeValue(testIsoString);
    expect(component.value).toBe(testIsoString);
    expect(component.inputControl.value).toBe('10.07.2020');
  });

  it('should set required', () => {
    expect(component.required).toBeFalsy();

    component.required = true;
    expect(component.required).toBeTruthy();
  });

  it('should set disabled', () => {
    expect(component.disabled).toBeFalsy();
    expect(component.inputControl.disabled).toBeFalsy();

    component.disabled = true;
    expect(component.disabled).toBeTruthy();
    expect(component.inputControl.disabled).toBeTruthy();
  });

  it('should setDisabledState', () => {
    expect(component.disabled).toBeFalsy();
    expect(component.inputControl.disabled).toBeFalsy();

    component.setDisabledState(true);
    expect(component.disabled).toBeTruthy();
    expect(component.inputControl.disabled).toBeTruthy();
  });

  it('should get empty true if controls are empty', () => {
    expect(component.empty).toBeTruthy();

    component.value = testIsoString;
    expect(component.empty).toBeFalsy();
  });

  it('should set placeholder', () => {
    expect(component.placeholder).toBe('');

    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state false', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should init controls', () => {
    expect(component.dateControl).toBeDefined();
    expect(component.inputControl).toBeDefined();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-date-picker');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.apply(new MouseEvent('click'));
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    component.apply(new MouseEvent('click'));
    expect(test).toBe(true);
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should set config', () => {
    expect(component.config).toEqual(new SwuiDatePickerConfigModel(defaultConfig));

    component.config = testConfig;
    expect(component.config).toEqual(new SwuiDatePickerConfigModel(testConfig));
  });

  it('should format value', () => {
    component.value = testIsoString;
    expect(component.inputControl.value).toBe('10.07.2020');

    component.config = testConfig;
    expect(component.inputControl.value).toBe('07:10 08:00 CST');
  });

  it('should cancel', () => {
    component.value = testIsoString;
    component.dateControl.setValue('2020-07-10T10:10:10.000Z');
    component.cancel(createFakeEvent('click'));
    expect(component.menuTrigger ? component.menuTrigger.menuOpen : true).toBeFalsy();
    expect(component.value).toBe(testIsoString);
  });

  it('should apply', () => {
    component.value = testIsoString;
    const newValue = '2020-07-10T10:10:10.000Z';
    component.dateControl.setValue(newValue);
    component.apply(createFakeEvent('click'));
    expect(component.menuTrigger ? component.menuTrigger.menuOpen : true).toBeFalsy();
    expect(component.value).toBe(newValue);
  });

  it('should clear', () => {
    component.value = testIsoString;
    component.clear(createFakeEvent('click'));
    expect(component.dateControl.value).toBe(null);
    component.apply(createFakeEvent('click'));
    expect(component.value as any).toBe(null);
  });

});

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}
