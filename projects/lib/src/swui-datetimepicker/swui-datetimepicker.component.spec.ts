import { DebugElement, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { FormGroup } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { MatMenuTrigger } from '@angular/material/menu';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import * as moment from 'moment';

import { SwuiDatetimepickerComponent } from './swui-datetimepicker.component';
import { DATETIMEPICKER_MODULES } from './swui-datetimepicker.module';
import { SwuiDateTimepickerConfig } from './swui-datetimepicker.interface';


describe('SwuiDatetimepickerComponent', () => {
  let component: SwuiDatetimepickerComponent;
  let fixture: ComponentFixture<SwuiDatetimepickerComponent>;
  let config: SwuiDateTimepickerConfig;
  let testDate: string;
  let testMoment: moment.Moment;
  let menuTrigger: MatMenuTrigger;
  let host: DebugElement;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        ...DATETIMEPICKER_MODULES,
      ],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [SwuiDatetimepickerComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiDatetimepickerComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    config = {
      dateFormat: 'DD.MM.YYYY',
      disableTimepicker: false,
      timeDisableLevel: {
        hour: true,
        minute: true,
        second: true
      },
      timeFormat: 'HH:mm:ss'
    };
    testDate = '2019-01-14T09:20:06.246Z';
    testMoment = moment.parseZone(testDate);
    menuTrigger = fixture.debugElement.query(By.directive(MatMenuTrigger)).injector.get(MatMenuTrigger);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testDate;
    expect(component.value).toBe(testDate);
    expect(component.dateSource).toEqual(testMoment);
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBe(coerceBooleanProperty(true));
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBe(coerceBooleanProperty(true));
  });

  it('should set enabled', () => {
    component.disabled = false;
    expect(component.disabled).toBe(coerceBooleanProperty(false));
  });

  it('should get empty true if controls are empty', () => {
    expect(component.empty).toBe(true);
  });

  it('should get empty false if controls are not empty', () => {
    component.value = testDate;
    expect(component.empty).toBe(false);
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state false', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should init form', () => {
    expect(component.form).toBeDefined();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-datetimepicker');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should shouldLabelFloat to be true when not empty', () => {
    component.value = testDate;
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should shouldLabelFloat to be true when focused', () => {
    component.onClick();
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should call onTouched on close menu', () => {
    spyOn(component, 'onTouched');
    component.onClose();
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    component.onClick();
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should shouldLabelFloat to be false when disabled', () => {
    component.disabled = true;
    component.onClick();
    expect(component.shouldLabelFloat).toBe(false);
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should set config', () => {
    component.config = config;
    expect(component.config).toEqual(config);
  });

  it('should disable timepicker', () => {
    component.config = { disableTimepicker: true };
    const time = component.form.get('time') as FormGroup;
    expect(time.disabled).toBe(true);
  });

  it('should enable timepicker', () => {
    component.config = { disableTimepicker: false };
    const time = component.form.get('time') as FormGroup;
    expect(time.disabled).toBe(false);
  });

  it('should write value', () => {
    component.writeValue(testDate);
    expect(component.dateSource).toEqual(testMoment);
  });

  it('should set time from string date', () => {
    component.writeValue(testDate);
    const time = component.form.get('time') as FormGroup;
    expect(getProcessedTime(testMoment)).toEqual(time.value);
  });

  it('should set time from Moment', () => {
    component.writeValue(testMoment);
    const time = component.form.get('time') as FormGroup;
    expect(getProcessedTime(testMoment)).toEqual(time.value);
  });

  it('should set date from string date', () => {
    component.writeValue(testDate);
    const date = component.form.get('date') as FormGroup;
    expect(testMoment).toEqual(date.value);
  });

  it('should set date from Moment', () => {
    component.writeValue(testMoment);
    const date = component.form.get('date') as FormGroup;
    expect(testMoment).toEqual(date.value);
  });

  it('should format Moment to string for placeholder', () => {
    component.writeValue(testDate);
    const expectedDate = testMoment.format(config.dateFormat + ' ' + config.timeFormat);
    expect(expectedDate).toEqual(component.formattedDate);
  });

  it('should close popup onCancel click', () => {
    spyOn(menuTrigger, 'closeMenu');
    component.onCancel(new MouseEvent('click'));
    expect(menuTrigger.closeMenu).toHaveBeenCalledTimes(1);
  });

  it('should close popup onSubmit click', () => {
    spyOn(menuTrigger, 'closeMenu');
    component.onSubmit(new MouseEvent('click'));
    expect(menuTrigger.closeMenu).toHaveBeenCalledTimes(1);
  });

  it('should prevent close menu on click', () => {
    component.preventClose(new MouseEvent('click'));
    spyOn(menuTrigger, 'closeMenu');
    expect(menuTrigger.closeMenu).toHaveBeenCalledTimes(0);
  });

  it('should set formatted date on submit', () => {
    component.form.patchValue({
      date: testMoment,
      time: getProcessedTime(testMoment)
    });
    component.onSubmit(new MouseEvent('click'));
    const expectedDate = testMoment.format(config.dateFormat + ' ' + config.timeFormat);
    expect(expectedDate).toEqual(component.formattedDate);
  });

  it('should get value from form', () => {
    component.form.patchValue({
      date: testMoment,
      time: getProcessedTime(testMoment)
    });
    component.onSubmit(new MouseEvent('click'));
    expect(component.dateSource).toEqual(testMoment);
  });

  it('should set today if there is no selected date', () => {
    component.onSubmit(new MouseEvent('click'));
    expect(component.dateSource).toBeDefined();
    if (component.dateSource) {
      expect(component.dateSource.format()).toEqual(moment.utc().startOf('day').format());
    }
  });

  it('should call onChange on submit', () => {
    spyOn(component, 'onChange');
    component.onSubmit(new MouseEvent('click'));
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should disable form', () => {
    component.setDisabledState(true);
    expect(component.form.disabled).toBe(true);
  });

  it('should enable form', () => {
    component.setDisabledState(false);
    expect(component.form.disabled).toBe(false);
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.onSubmit(new MouseEvent('click'));
    expect(test).toBe(true);
  });

});


function getProcessedTime( value: moment.Moment ) {
  return {
    hour: value.hours(),
    minute: value.minutes(),
    second: value.second(),
  };
}

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}
