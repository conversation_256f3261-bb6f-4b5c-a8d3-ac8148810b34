import { Component, EventEmitter, Input, OnDestroy, Optional, Output, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CUSTOM_PERIODS, CustomPeriod, CustomPeriods } from './custom-period.interface';
import { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';

@Component({
  selector: 'lib-swui-custom-period',
  templateUrl: './custom-period.component.html',
  styleUrls: ['./custom-period.component.scss']
})
export class CustomPeriodComponent implements OnDestroy {
  @Input() hideCustomPeriods = false;
  @Input() smallCustomPeriodsButton = false;
  @Input() disabled = false;
  @Input() title = 'COMPONENTS.DATERANGE.customPeriod';
  @Input() periods: CustomPeriod[] = CUSTOM_PERIODS;
  @Output() periodChange = new EventEmitter<CustomPeriods>();

  @ViewChild(MatMenuTrigger) trigger?: MatMenuTrigger;

  currentPeriod: CustomPeriod | undefined;

  private destroy$ = new Subject();

  constructor( @Optional() filter: SwuiTopFilterDataService ) {
    if (filter) {
      filter.onReset
        .pipe(
          takeUntil(this.destroy$)
        )
        .subscribe(() => {
          this.currentPeriod = undefined;
        });
    }
  }

  onClick( event: Event, period: CustomPeriod ) {
    event.preventDefault();
    if (this.trigger) {
      this.trigger.closeMenu();
    }
    this.currentPeriod = period;
    this.periodChange.emit(period.fn());
  }

  reset() {
    this.currentPeriod = undefined;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

}
