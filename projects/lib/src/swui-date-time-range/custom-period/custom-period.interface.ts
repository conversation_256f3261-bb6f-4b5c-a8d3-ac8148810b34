import * as moment from 'moment';


export interface CustomPeriod {
  title: string;
  fn: () => CustomPeriods;
}

export interface CustomPeriods {
  from: moment.Moment;
  to: moment.Moment;
}

export const CUSTOM_PERIODS: CustomPeriod[] = [
  {
    title: 'COMPONENTS.DATERANGE.today',
    fn: () => {
      const from = moment.utc().set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const to = from.clone().add(1, 'd');
      return {from, to};
    },
  },
  {
    title: 'COMPONENTS.DATERANGE.yesterday',
    fn: () => {
      const from = moment.utc().subtract(1, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const to = from.clone().add(1, 'd');
      return {from, to};
    },
  },
  {
    title: 'COMPONENTS.DATERANGE.last3Days',
    fn: () => {
      const to = moment.utc().add(1, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const from = to.clone().subtract(3, 'd');
      return {from, to};
    },
  },
  {
    title: 'COMPONENTS.DATERANGE.last7Days',
    fn: () => {
      const to = moment.utc().add(1, 'd').set({hour: 0, minute: 0, second: 0, millisecond: 0});
      const from = to.clone().subtract(7, 'd');
      return {from, to};
    },
  },
  {
    title: 'COMPONENTS.DATERANGE.thisMonth',
    fn: () => {
      const from = moment.utc().startOf('month');
      const to = from.clone().add(1, 'month');
      return {from, to};
    },
  },
  {
    title: 'COMPONENTS.DATERANGE.lastMonth',
    fn: () => {
      const from = moment.utc().subtract(1, 'month').startOf('month');
      const to = moment.utc().startOf('month');
      return {from, to};
    },
  },
];
