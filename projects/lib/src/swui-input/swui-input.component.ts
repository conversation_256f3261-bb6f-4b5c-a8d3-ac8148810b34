import { Component, ElementRef, forwardRef, Input, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ThemePalette } from '@angular/material/core';
import { FloatLabelType } from '@angular/material/form-field';

@Component({
  selector: 'lib-swui-input',
  templateUrl: './swui-input.component.html',
  styleUrls: [
    './swui-input.component.scss',
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwuiInputComponent),
      multi: true,
    }
  ]
})
export class SwuiInputComponent implements ControlValueAccessor {

  @Input() label = '';
  @Input() placeholder = '';
  @ViewChild('input', { static: true }) input: ElementRef | undefined;

  @Input() color: ThemePalette;
  @Input() floatLabel: FloatLabelType | undefined;
  @Input() hideRequiredMarker: boolean | undefined;
  @Input() hintLabel: string | undefined;

  disabled = false;

  onChange: any = () => {
  };

  onTouch: any = (data: any) => {
    if (this.input) {
      this.input.nativeElement.value = data.trim();
    }
  };

  writeValue( value: any ) {
    if (this.input) {
      this.input.nativeElement.value = value;
    }
  }

  registerOnChange( fn: any ) {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ) {
    this.onTouch = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }
}
