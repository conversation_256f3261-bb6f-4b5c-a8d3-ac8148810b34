import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata, storiesOf } from '@storybook/angular';

import { SwuiInputComponent } from './swui-input.component';
import { SwuiInputModule } from './swui-input.module';

storiesOf('Forms/Input', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiInputModule
      ],
    })
  )
  .add('Default input without options', () => ({
    component: SwuiInputComponent
  }))
  .add('Input with predefined value, label and placeholder', () => ({
    component: SwuiInputComponent,
    props: {
      label: 'Input label',
      placeholder: 'Search Item',
      ngModel: 'Init value'
    }
  }));
