import { Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';
import { NotificationsMessage } from '../swui-notifications.service';

@Component({
  selector: 'lib-swui-snackbar',
  templateUrl: './swui-snackbar.component.html',
  styleUrls: ['./swui-snackbar.component.scss'],
})
export class SwuiSnackbarComponent implements OnDestroy {

  constructor(
    public snackbar: MatSnackBarRef<SwuiSnackbarComponent>,
    @Inject(MAT_SNACK_BAR_DATA) public data: NotificationsMessage,
  ) {
  }

  ngOnDestroy() {
    this.close();
  }

  close() {
    if (this.snackbar) {
      this.snackbar.dismiss();
    }
  }
}
