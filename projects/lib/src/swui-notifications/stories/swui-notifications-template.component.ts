import { Component } from '@angular/core';

import { SwuiNotificationsService } from '../swui-notifications.service';

@Component({
  selector: 'lib-swui-notifications-template',
  templateUrl: './swui-notifications-template.component.html',
})
export class SwuiNotificationsTemplateComponent {

  constructor( private notifications: SwuiNotificationsService ) {
  }

  showNotification( type: string ) {
    switch (type) {
      case 'success':
        this.notifications.success('Success message', 'default');
        break;
      case 'error':
        this.notifications.error('Error message', '(4 sec) top', 'top', undefined, 4000);
        break;
      case 'warning':
        this.notifications.warning('Warning message', '(5 sec) top right', 'top', 'right', 5000);
        break;
    }
  }
}
