import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';

import { SwuiNotificationsService } from './swui-notifications.service';
import { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';
import { MAT_SNACK_BAR_DEFAULT_OPTIONS, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

export const notificationsMatModules = [
  MatButtonModule,
  MatIconModule,
  MatSnackBarModule,
];

@NgModule({
  imports: [
    CommonModule,
    ...notificationsMatModules,
  ],
  declarations: [
    SwuiSnackbarComponent,
  ],
  entryComponents: [
    SwuiSnackbarComponent,
  ],
  exports: [
    ...notificationsMatModules,
  ],
})
export class SwuiNotificationsModule {

  static forRoot(): ModuleWithProviders<SwuiNotificationsModule> {
    return {
      ngModule: SwuiNotificationsModule,
      providers: [
        SwuiNotificationsService,
        { provide: MAT_SNACK_BAR_DEFAULT_OPTIONS, useValue: { duration: 2500 } }
      ]
    };
  }
}
