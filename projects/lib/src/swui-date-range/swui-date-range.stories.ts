import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import * as moment from 'moment/moment';
import { I18nModule } from '../i18n.module';

import { SwuiDateRangeModule } from './swui-date-range.module';
import { SwuiDateRangeComponent } from './swui-date-range.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';

const EN = require('./locale.json');

export const template = `
  <mat-card style="margin: 32px">
    <mat-form-field appearance="outline" style="width: 500px">
    <mat-label>Test control label</mat-label>
    <lib-swui-date-range
      [title]="title"
      [config]="config"
      [disabled]="disabled"
      [minDate]="minDate"
      [maxDate]="maxDate"
      [customPeriods]="config?.customPeriods"
      [value]="value">
    </lib-swui-date-range>
    <mat-icon matPrefix>search</mat-icon>
  </mat-form-field>
</mat-card>
`;


storiesOf('Date/Date Range', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiDateRangeModule,
        MatFormFieldModule,
        MatCardModule,
        MatIconModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title'
    },
    template: template,
  }))
  .add('value', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title',
      value: { from: '2020-08-13T23:00:00.000Z', to: '2020-08-21T23:00:00.000Z' }
    },
    template: template,
  }))
  .add('config', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title',
      value: { from: '2020-08-13T00:00:00.000Z', to: '2020-08-21T00:00:00.000Z' },
      config: {
        dateFormat: 'YY/MM/DD',
        timePicker: true,
        timeFormat: 'HH:mm z',
        timeZone: 'Asia/Taipei',
        timeDisableLevel: {
          hour: true,
          minute: false,
          second: false
        }
      }
    },
    template: template,
  }))
  .add('disabled', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title',
      value: { from: '2020-08-13T23:00:00.000Z', to: '2020-08-21T23:00:00.000Z' },
      disabled: true
    },
    template: template,
  }))
  .add('min max', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title',
      minDate: '2020-08-10T00:00:00.000Z',
      maxDate: '2020-08-28T00:00:00.000Z'
    },
    template: template,
  }))
  .add('max month period', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title',
      value: { from: '2022-05-15T11:00:00.000Z', to: '2022-06-15T10:59:59.000Z' },
      config: {
        maxPeriod: 'month',
        timePicker: true,
        timeDisableLevel: {
          hour: true,
          minute: true,
          second: true
        },
        timeZone: 'Asia/Tbilisi'
      },
    },
    template: template,
  }))
  .add('custom periods', () => ({
    component: SwuiDateRangeComponent,
    props: {
      title: 'Test title',
      config: {
        customPeriods: [
          {
            title: 'COMPONENTS.DATE_RANGE.last7Days',
            fn: ( timezone?: string ) => {
              const date = timezone ? moment().tz(timezone) : moment.utc();
              const to = date.clone().endOf('day');
              const from = date.clone().startOf('day').subtract(6, 'd');
              return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
            },
          },
        ]
      }
    },
    template: template,
  }));

