import * as moment_ from 'moment';
const moment = moment_;

export interface SwuiDateRange {
  from: string;
  to: string;
}

export class SwuiDateRangeModel {
  from: string;
  to: string;

  constructor( data?: SwuiDateRange ) {
    this.from = data ? data.from : '';
    this.to = data ? data.to : '';
  }
}

export interface CustomPeriod {
  title: string;
  fn: ( timezone?: string, chooseStart?: boolean ) => SwuiDateRange;
}

export const CUSTOM_PERIODS: CustomPeriod[] = [
  {
    title: 'COMPONENTS.DATE_RANGE.last7Days',
    fn: ( timezone?: string, chooseStart?: boolean ) => {
      const date = timezone ? moment().tz(timezone) : moment.utc();
      const to = chooseStart
        ? date.clone().add(1, 'day').startOf('day')
        : date.clone().endOf('day');
      const from = date.clone().startOf('day').subtract(6, 'd');
      return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.last14Days',
    fn: ( timezone?: string, chooseStart?: boolean ) => {
      const date = timezone ? moment().tz(timezone) : moment.utc();
      const to = chooseStart
        ? date.clone().add(1, 'day').startOf('day')
        : date.clone().endOf('day');
      const from = date.clone().startOf('day').subtract(13, 'd');
      return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.last30Days',
    fn: ( timezone?: string, chooseStart?: boolean ) => {
      const date = timezone ? moment().tz(timezone) : moment.utc();
      const to = chooseStart
        ? date.clone().add(1, 'day').startOf('day')
        : date.clone().endOf('day');
      const from = date.clone().startOf('day').subtract(29, 'd');
      return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.last90Days',
    fn: ( timezone?: string, chooseStart?: boolean ) => {
      const date = timezone ? moment().tz(timezone) : moment.utc();
      const to = chooseStart
        ? date.clone().add(1, 'day').startOf('day')
        : date.clone().endOf('day');
      const from = date.clone().startOf('day').subtract(89, 'd');
      return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.thisMonth',
    fn: ( timezone?: string, chooseStart?: boolean ) => {
      const date = timezone ? moment().tz(timezone) : moment.utc();
      const from = date.startOf('month');
      const to = chooseStart
        ? from.clone().add(1, 'month').startOf('day')
        : from.clone().add(1, 'month').add(-1, 'day').endOf('day');
      return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
    },
  },
  {
    title: 'COMPONENTS.DATE_RANGE.lastMonth',
    fn: ( timezone?: string, chooseStart?: boolean ) => {
      const date = timezone ? moment().tz(timezone) : moment.utc();
      const from = date.clone().subtract(1, 'month').startOf('month');
      const to = chooseStart
        ? from.clone().endOf('month').add(1, 'day').startOf('day')
        : from.clone().endOf('month').endOf('day');
      return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };
    },
  },
];
