import { Component, Input, OnInit } from '@angular/core';
import { ControlContainer, FormControl, FormGroup, } from '@angular/forms';

export interface SwuiNumericRangeOptions {
  placeholder?: [string, string];
  enableCloseButton?: [boolean, boolean] | boolean;
  errorMessages?: [{ [key: string]: string }, { [key: string]: string }] | { [key: string]: string };
}

@Component({
  selector: 'lib-swui-numeric-range',
  templateUrl: './swui-numeric-range.component.html',
})
export class SwuiNumericRangeComponent implements OnInit {
  @Input('options')
  set setOptions(options: SwuiNumericRangeOptions) {
    if (Array.isArray(options.enableCloseButton)) {
      this.fromIsCloseEnable = Boolean(options.enableCloseButton[0]);
      this.toIsCloseEnable = Boolean(options.enableCloseButton[1]);
    } else {
      this.fromIsCloseEnable = this.toIsCloseEnable = Boolean(options.enableCloseButton);
    }
    if (Array.isArray(options.placeholder)) {
      this.fromPlaceholder = options.placeholder[0] || '';
      this.toPlaceholder = options.placeholder[1] || '';
    }
    if (Array.isArray(options.errorMessages)) {
      this.fromTransformFn = options.errorMessages[0];
      this.toTransformFn = options.errorMessages[1];
    } else {
      this.fromTransformFn = this.toTransformFn = options.errorMessages;
    }
  }

  fromControl: FormControl = new FormControl('');
  toControl: FormControl = new FormControl('');

  fromIsCloseEnable = true;
  toIsCloseEnable = true;

  fromPlaceholder = '';
  toPlaceholder = '';

  fromTransformFn: any = null;
  toTransformFn: any = null;

  constructor(private controlContainer: ControlContainer) {
  }

  ngOnInit() {
    if (this.controlContainer.control) {
      const formGroup = this.controlContainer.control as FormGroup;
      let keys = Object.keys(formGroup.controls);
      if (keys.length !== 2) {
        if (!keys[0]) {
          formGroup.addControl('_from', new FormControl(''));
        }
        if (!keys[1]) {
          formGroup.addControl('_to', new FormControl(''));
        }
        keys = Object.keys(formGroup.controls);
      }
      this.fromControl = formGroup.get(keys[0]) as FormControl;
      this.toControl = formGroup.get(keys[1]) as FormControl;
    }
  }
}
