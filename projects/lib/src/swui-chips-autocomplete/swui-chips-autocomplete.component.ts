import { FocusMonitor } from '@angular/cdk/a11y';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';
import { FormControl, FormGroupDirective, NgControl } from '@angular/forms';
import { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { ErrorStateMatcher, MatOptionSelectionChange } from '@angular/material/core';
import { MatFormFieldControl } from '@angular/material/form-field';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { startWith, switchMap, take, tap } from 'rxjs/operators';

import { OptionModel } from '../swui-autoselect/option.model';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';

const CONTROL_NAME = 'lib-swui-chips-autocomplete';
let nextUniqueId = 0;

@Component({
  selector: 'lib-swui-chips-autocomplete',
  templateUrl: './swui-chips-autocomplete.component.html',
  styleUrls: [
    './swui-chips-autocomplete.component.scss',
  ],
  providers: [{ provide: MatFormFieldControl, useExisting: SwuiChipsAutocompleteComponent }],
})
export class SwuiChipsAutocompleteComponent extends SwuiMatFormFieldControl<string[]> implements OnInit {
  @Input()
  get value(): string[] {
    return this._value;
  }

  set value( value: string[] ) {
    this._value = value;
    this.setSelectedItems(value);
    this.stateChanges.next();
  }

  @Input() searchFn?: ( text: string ) => Observable<any>;
  @Input() addFn?: ( text: string ) => Observable<any>;
  @Input() mapFn?: ( text: string ) => any;

  @Input()
  set items( value: OptionModel[] ) {
    if (!!value) {
      this.itemsMap.clear();
      value.forEach(item => {
        this.itemsMap.set(item.id, { ...item });
      });
      this._items = value;
      this.inputFormControl.setValue(this.inputFormControl.value);
      this.setSelectedItems(this.selectedItems$.value);
    }
  }

  get items(): OptionModel[] {
    return this._items;
  }

  get empty() {
    return this.isEmpty;
  }

  filteredItems: Observable<OptionModel[]> = new Observable<OptionModel[]>();
  readonly inputFormControl = new FormControl();
  readonly itemsMap = new Map();
  selectedItems: string[] = [];
  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  isEmpty = true;
  readonly controlType = CONTROL_NAME;
  hasFounded = true;

  readonly selectedItems$ = new BehaviorSubject<string[]>([]);

  @ViewChild(MatInput) input?: MatInput;
  @ViewChild('input', { read: MatAutocompleteTrigger }) autoCompleteTrigger: MatAutocompleteTrigger | undefined;
  @ViewChild('auto') matAutocomplete: MatAutocomplete | undefined;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  private _value: string[] = [];
  private _items: OptionModel[] = [];
  private blockBlurEvent = false;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher
  ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
  }

  ngOnInit() {
    this.selectedItems$
      .subscribe(( val: string[] ) => {
        this.selectedItems = val;
        this._value = this.selectedItems;
        this.isEmpty = this.selectedItems.length === 0;
        this.onChange(this._value.map(item => {
          return this.mapFn ? this.mapFn(item) : item;
        }));
      });
    this.initFilteredItems();
  }

  closePanel() {
    if (!this.blockBlurEvent) {
      this.autoCompleteTrigger?.closePanel();
    }
    this.blockBlurEvent = false;
  }

  onContainerClick( event: Event ) {
    event.stopPropagation();
    if (this.input && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.input.focus();
    }
  }

  writeValue( value: string[] ): void {
    this.setSelectedItems(value);
  }

  initFilteredItems() {
    const filter = ( text: string ) => {
      return of(text ? this.filter(text) : this.items.slice());
    };
    const search = this.searchFn || filter;

    let value = '';

    this.filteredItems = this.inputFormControl.valueChanges.pipe(
      startWith((this.inputFormControl.value) as string),
      tap(text => {
        value = text && text.toString() || '';
      }),
      switchMap(() => search(value)),
      tap(items => this.hasFounded = items.some(item => item.text.toLowerCase() === value.toLowerCase()))
    );
  }

  getItemText( id: string ) {
    const item = this.itemsMap.get(id);
    return item ? item.text : '';
  }

  add( option: MatOptionSelectionChange ) {
    const value = option.source.value;

    if ((value || '').trim() && this.addFn) {
      this.addFn(value)
        .pipe(take(1))
        .subscribe(tag => {
          this.itemsMap.set(tag.id, { ...tag });
          this.selectItem(tag.id);
          this.inputFormControl.setValue('');
        });
    }
  }

  remove( id: string ): void {
    const index = this.selectedItems.indexOf(id);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
      setTimeout(() => {
        this.selectedItems$.next(this.selectedItems);
      }, 0);
      this.enableOptionById(id);
    }
  }

  selected( event: MatOptionSelectionChange ): void {
    if (event.isUserInput) {
      const item = event.source.value;

      if (this.itemsMap.has(item.id)) {
        if (this.itemsMap.get(item.id).disabled || this.itemsMap.get(item.id).selected) {
          return;
        }
      } else {
        this.itemsMap.set(item.id, { ...item });
      }

      this.selectItem(item.id);
    }
  }

  clear(): void {
    if (this.input) {
      this.input.value = '';
    }
    this.inputFormControl.setValue(null);
  }

  onMenuItemMousedown() {
    this.blockBlurEvent = true;
  }

  selectItem( id?: string ) {
    if (!id) {
      return;
    }

    this.disableOptionById(id);
    this.selectedItems.push(id);
    this.selectedItems$.next(this.selectedItems);
  }

  onInputClick() {
    this.autoCompleteTrigger?.openPanel();
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }

  private filter( value: string ): OptionModel[] {
    return this.items
      .filter(item => item.text.toLowerCase().indexOf(value.toLowerCase()) > -1);
  }

  private disableOptionById( id: string ) {
    if (this.itemsMap.has(id)) {
      this.itemsMap.get(id).selected = true;
    }
  }

  private enableOptionById( id: string ) {
    if (this.itemsMap.has(id)) {
      this.itemsMap.get(id).selected = false;
    }
  }

  private setSelectedItems( value: any[] ) {
    if (Array.isArray(value)) {
      if (!value.length) {
        this.selectedItems?.forEach(id => this.enableOptionById(id));
      }

      this.selectedItems = value.map(item => {
        if (typeof item === 'string') {
          return item;
        }

        if (!this.itemsMap.has(item.id)) {
          this.itemsMap.set(item.id, { ...item });
        }

        return item.id;
      });
      this.selectedItems.forEach(id => this.disableOptionById(id));
      this.selectedItems$.next(this.selectedItems);
    }
  }
}
