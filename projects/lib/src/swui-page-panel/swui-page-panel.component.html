<mat-card class="top-bar">
  <div class="top-bar__inner" fxLayout="row">
    <div fxFlex fxLayout="row" fxLayoutAlign="start center">
      <a id="goBackBtn" mat-icon-button (click)="goBack($event)" *ngIf="back || backUrl">
        <mat-icon>arrow_back</mat-icon>
      </a>
      <h2 class="mat-title top-bar__title">
        {{ title | translate }}
      </h2>
    </div>
    <div>
      <ng-container *ngIf="actions && layout === 'menu'; else separateButtons">
        <button
          class="top-bar__button mat-button-md"
          mat-flat-button
          [matMenuTriggerFor]="actionsMenu"
          [color]="actionsMenuButtonColor">

          {{ (actionsMenuButtonTitle ? actionsMenuButtonTitle : 'COMPONENTS.PAGE_PANEL.ACTIONS') | translate }}
          <mat-icon class="top-bar__icon top-bar__icon--expand">expand_more</mat-icon>
        </button>
        <mat-menu #actionsMenu="matMenu" xPosition="before">
          <button
            *ngFor="let action of actions"
            class="top-bar__item"
            mat-menu-item
            [title]="action.hover"
            [ngStyle]="action.getStyle && action.getStyle()"
            [disabled]="isDisabled(action)"
            (click)="performAction($event, action)">

            <mat-icon
              *ngIf="action.icon || action.fontSet || action.fontIcon || action.svgIcon"
              class="top-bar__icon top-bar__icon--left top-bar__icon--menu"
              [fontSet]="action.fontSet"
              [fontIcon]="action.fontIcon"
              [svgIcon]="action.svgIcon"
              [ngStyle]="{
                  'margin-right.px': action.iconRightMargin,
                  'font-size.px': action.iconFontSize,
                  'width.px': action.iconWidth,
                  'height.px': action.iconHeight
                }">

              {{ action.icon }}
            </mat-icon>
            <span>{{ action.title | translate }}</span>
          </button>
        </mat-menu>
      </ng-container>

      <ng-template #separateButtons>
        <button
          *ngFor="let action of actions"
          class="top-bar__button mat-button-md"
          mat-flat-button
          [color]="action.color"
          [title]="action.hover"
          [disabled]="isDisabled(action)"
          [ngStyle]="action.getStyle && action.getStyle()"
          (click)="performAction($event, action)">

          <mat-icon
            *ngIf="action.icon || action.fontSet || action.fontIcon || action.svgIcon"
            class="top-bar__icon top-bar__icon--left"
            [fontSet]="action.fontSet"
            [fontIcon]="action.fontIcon"
            [svgIcon]="action.svgIcon"
            [ngStyle]="{
                'margin-right.px': action.iconRightMargin,
                'font-size.px': action.iconFontSize,
                'width.px': action.iconWidth,
                'height.px': action.iconHeight
              }">

            {{ action.icon }}
          </mat-icon>
          <span>{{ action.title | translate }}</span>
        </button>
      </ng-template>

    </div>
  </div>
</mat-card>
