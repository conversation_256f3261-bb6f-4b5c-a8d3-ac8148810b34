import { NgModule } from '@angular/core';
import { SwuiPagePanelComponent } from './swui-page-panel.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatDialogModule } from '@angular/material/dialog';
import { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    FlexLayoutModule,
    TranslateModule.forChild(),

    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDialogModule,
  ],
  exports: [
    SwuiPagePanelComponent,
  ],
  declarations: [
    SwuiPagePanelComponent,
    ActionConfirmDialogComponent,
  ],
  entryComponents: [
    ActionConfirmDialogComponent,
  ],
  providers: [],
})
export class SwuiPagePanelModule {
}

