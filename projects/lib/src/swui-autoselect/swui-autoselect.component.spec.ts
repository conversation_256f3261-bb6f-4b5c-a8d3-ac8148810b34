import { DebugElement } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiAutoselectComponent } from './swui-autoselect.component';
import { MOULES_AUTOSELECT } from './swui-autoselect.module';
import { OptionModel } from './option.model';


describe('SwuiAutoselectComponent', () => {
  let component: SwuiAutoselectComponent;
  let fixture: ComponentFixture<SwuiAutoselectComponent>;
  let host: DebugElement;
  let options: OptionModel[];
  let testValue: string;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        ...MOULES_AUTOSELECT,
      ],
      declarations: [SwuiAutoselectComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiAutoselectComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    options = [
      { id: 'test1', text: 'One' },
      { id: 'test2', text: 'Two' },
      { id: 'test3', text: 'Three' },
      { id: 'test4', text: 'Four', disabled: true },
      { id: 'test5', text: 'Five', disabled: true }
    ];
    testValue = 'test1';
    component.options = options;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testValue;
    expect(component.value).toEqual(testValue);

    component.value = undefined;
    expect(component.value).toBeUndefined();

    component.value = 'wrongValue';
    expect(component.value).toBeUndefined();

    component.isNotInOptionsValue = true;
    component.value = 'wrongValue';
    expect(component.value).toBe('wrongValue');
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBe(coerceBooleanProperty(true));
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBe(coerceBooleanProperty(true));

    component.disabled = false;
    expect(component.disabled).toBe(coerceBooleanProperty(false));
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get empty true if controls are empty', () => {
    expect(component.empty).toBe(true);
  });

  it('should get empty false if controls are not empty', () => {
    component.value = testValue;
    expect(component.empty).toBe(false);
  });

  it('should get error state', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-autoselect');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should shouldLabelFloat to be true when not empty', () => {
    component.value = testValue;
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should shouldLabelFloat to be true when host focused', () => {
    expect(component.shouldLabelFloat).toBe(false);

    dispatchFakeEvent(host.nativeElement, 'focus');
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should set host class floating when it is not empty', () => {
    component.value = testValue;
    fixture.detectChanges();
    expect(host.nativeElement.classList.contains('floating')).toBe(true);
  });

  it('should set host class floating when host focused', () => {
    dispatchFakeEvent(host.nativeElement, 'focus');
    fixture.detectChanges();
    expect(host.nativeElement.classList.contains('floating')).toBe(true);
  });

  it('should set aria-describedby on host', () => {
    expect(host.nativeElement.getAttribute('aria-describedby')).toBe('');
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should call onChange onInit when selected value changed', () => {
    spyOn(component, 'onChange');
    component.ngOnInit();
    component.value = testValue;
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should not set valueAccessor if form control', () => {
    (fixture.componentInstance as any).ngControl = new FormControl(testValue);
    expect(component.ngControl.valueAccessor).toBeUndefined();
  });

  it('should disable control', () => {
    component.setDisabledState(true);
    expect(component.disabled).toBe(true);

    component.setDisabledState(false);
    expect(component.disabled).toBe(false);
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.writeValue(testValue);
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(test).toBe(true);
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should set options', () => {
    component.options = options;
    expect(component.options).toEqual(options);
  });
});

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

