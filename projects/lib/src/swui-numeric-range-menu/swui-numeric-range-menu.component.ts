import { FocusMonitor } from '@angular/cdk/a11y';
import {
  ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormGroupDirective, NgControl } from '@angular/forms';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatMenuTrigger } from '@angular/material/menu';
import { BehaviorSubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';

export interface SwuiNumericRange {
  from: string;
  to: string;
}

function toControlValue( val: SwuiNumericRange ): string {
  const { from, to } = val || {};
  return `${from || ''}${!!from && !!to ? ' - ' : ''}${to || ''}`;
}

const CONTROL_NAME = 'lib-swui-numeric-range-menu';
let nextUniqueId = 0;

@Component({
  selector: 'lib-swui-numeric-range-menu',
  templateUrl: 'swui-numeric-range-menu.component.html',
  styleUrls: ['swui-numeric-range-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [{ provide: MatFormFieldControl, useExisting: NumericRangeMenuComponent }],
})
export class NumericRangeMenuComponent extends SwuiMatFormFieldControl<SwuiNumericRange> implements OnInit {
  @Input()
  set value( val: SwuiNumericRange ) {
    const value = { ...val };
    this.transformFromValue(value);
    this.valueControl.setValue(toControlValue(value));
    this._value$.next(value);
  }

  get value(): SwuiNumericRange {
    return this._value$.value;
  }

  @Input() title = '';
  @Input() set divider(divider: number) {
    this._divider = divider || 1;

    const value = { ...this.form.value };
    this.transformToValue(value);
    this.onChange(value);
  }

  get divider(): number {
    return this._divider || 1;
  }

  @Input() set requiredField( value: string ) {
    this._requiredField = value;

    if (!value) {
      this.form.enable({ emitEvent: false });
      return;
    }

    this.form.disable({ emitEvent: false });
  }

  get requiredField(): string {
    return this._requiredField;
  }

  get empty() {
    return !this.valueControl.value;
  }

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;
  readonly controlType = CONTROL_NAME;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return !this.empty;
  }

  @ViewChild('range', { read: MatMenuTrigger }) menuTriggerRef: MatMenuTrigger | undefined;
  @ViewChild('input') input?: MatInput;

  readonly valueControl = new FormControl('');
  readonly form: FormGroup;
  selectedIndex = 0;
  _divider = 1;

  private readonly _value$ = new BehaviorSubject({ from: '', to: '' });
  private _requiredField = '';

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               fb: FormBuilder
  ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
    this.form = fb.group({
      from: [],
      to: []
    });
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(val => {
      this._value$.next(val);
    });
  }

  onContainerClick( event: Event ): void {
    event.stopPropagation();
    if (this.elRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.elRef.nativeElement.focus();
    }
  }

  writeValue( val: SwuiNumericRange ): void {
    const empty = { from: '', to: '' };
    const value: SwuiNumericRange = { ...empty, ...val };
    this.transformFromValue(value);
    this.form.setValue(value, { emitEvent: false });
    this.valueControl.setValue(toControlValue(value));
  }

  get fromControl(): FormControl {
    return this.form.get('from') as FormControl;
  }

  get toControl(): FormControl {
    return this.form.get('to') as FormControl;
  }

  prevent( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
  }

  clear( event: Event ) {
    event.preventDefault();
    this.form.setValue({ from: '', to: '' });
  }

  cancel( event: Event ) {
    event.preventDefault();
    if (this.menuTriggerRef) {
      this.menuTriggerRef.closeMenu();
    }
  }

  apply( event: Event ) {
    event.preventDefault();
    this._value$.next(this.form.value);

    const value = { ...this.form.value };
    this.transformToValue(value);

    this.valueControl.setValue(toControlValue(this.form.value));
    this.onChange(value);
    if (this.menuTriggerRef) {
      this.menuTriggerRef.closeMenu();
    }
  }

  onSelectedIndexChange( tabIndex: number ) {
    this.selectedIndex = tabIndex;
  }

  protected onDisabledState( disabled: boolean ) {
    disabled ? this.valueControl.disable() : this.valueControl.enable();
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }

  private transformFromValue( value: SwuiNumericRange ) {
    value.from = value.from && `${Number(value.from) / this.divider}`;
    value.to = value.to && `${Number(value.to) / this.divider}`;
  }

  private transformToValue( value: SwuiNumericRange ) {
    value.from = value.from && `${Number(value.from) * this.divider}`;
    value.to = value.to && `${Number(value.to) * this.divider}`;
  }
}
