import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ColorPickerDirective } from 'ngx-color-picker';
import { ColorInputOption } from '../dynamic-form.model';


@Component({
  selector: 'lib-input-color',
  templateUrl: './input-color.component.html',
  styleUrls: ['./input-color.component.scss']
})
export class InputColorComponent implements OnInit {
  @Input() control?: FormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: ColorInputOption | undefined ) {
    this.title = value?.title;
    this.defaultValue = value?.defaultValue;
    this.required = value?.required;
    this.errorMessages = {
      invalidColorHexFormat: 'VALIDATION.invalidColorHexFormat',
      ...(value?.validation?.messages || {})
    };
  }

  title?: string;
  defaultValue?: string;
  required?: boolean;
  errorMessages?: { [key: string]: string };

  isActive = false;
  isIdentical = false;

  @ViewChild(ColorPickerDirective, { static: true }) private colorPicker?: ColorPickerDirective;

  ngOnInit() {
    if (this.control) {
      this.isIdentical = this.control.value === this.defaultValue;
    }
  }

  onToggleDialog( isOpen: boolean ) {
    this.isActive = isOpen;
  }

  onResetClick( event: Event ) {
    event.preventDefault();
    event.stopPropagation();

    if (this.colorPicker) {
      this.colorPicker.closeDialog();
    }
    if (this.isIdentical) {
      return;
    }
    if (this.control) {
      this.control.setValue(this.defaultValue);
    }
    this.isIdentical = true;
  }

  handleColorChange( color: string ) {
    if (this.control) {
      this.control.setValue(color);
    }
    this.isIdentical = color === this.defaultValue;
  }
}
