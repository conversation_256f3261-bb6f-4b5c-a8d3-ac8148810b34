import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { NumericRangeInputOption } from '../dynamic-form.model';

@Component({
  selector: 'lib-input-numeric-range',
  templateUrl: './input-numeric-range.component.html',
  styleUrls: ['./input-numeric-range.component.scss']
})
export class InputNumericRangeComponent {
  @Input() control?: FormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: NumericRangeInputOption | undefined ) {
    this.title = value?.title;
    this.clearable = value?.clearable;
    this.dependsOn = value?.dependsOn;
    this.dependsOnFieldName = value?.dependsOnFieldName;
    this.getDivider = value?.getDivider;
    this.subscribeToDepend();
  }

  title?: string;
  clearable?: boolean;
  dependsOn?: string;
  dependsOnFieldName?: string;
  divider = 1;
  getDivider?: ( value: string ) => number;
  private destroy$ = new Subject();

  constructor( private cdr: ChangeDetectorRef ) {
  }

  get dependsOnValue(): string | undefined {
    const formValue = this.control?.parent?.getRawValue();

    if (!formValue) {
      return this.dependsOnFieldName;
    }

    if (!this.dependsOn) {
      return;
    }

    return formValue[this.dependsOn] ? undefined : this.dependsOnFieldName;
  }

  isEmpty() {
    const value = this.control?.value;
    return !value || Object.values(value).every(item => !item);
  }

  clear( event?: MouseEvent ) {
    event?.stopPropagation();
    if (this.control) {
      this.control.setValue({});
    }
  }

  private subscribeToDepend() {
    this.destroy$.next();
    const formControl = this.dependsOn && this.control?.parent?.get(this.dependsOn);
    if (!this.dependsOn || !formControl) {
      return;
    }

    this.divider = this.getDivider && this.getDivider(formControl.value) || 1;

    (formControl as FormControl).valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(( value: string ) => {
        if (!value) {
          this.clear();
        }

        this.divider = this.getDivider && this.getDivider(value) || 1;
        this.cdr.detectChanges();
      });
  }
}
