import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ControlInputItem, InputOption } from '../dynamic-form.model';

@Component({
  selector: 'lib-control-input',
  templateUrl: './control-input.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ControlInputComponent {
  @Input() item?: ControlInputItem;
  @Input() prefixId = '';
  @Input() readonly = false;
  @Input() submitted = false;

  get type(): any {
    const type = this.option?.type;
    if (type) {
      if (['text', 'url', 'textarea', 'string'].includes(type)) {
        return 'text';
      }
      if (['select', 'multiselect'].includes(type)) {
        return 'select';
      }
      if (['daterange', 'datetimerange'].includes(type)) {
        return 'daterange';
      }
    }
    return type;
  }

  get formControl(): FormControl | undefined {
    return this.item?.control;
  }

  get option(): InputOption | undefined {
    return this.item?.option;
  }

  get id(): string {
    return this.prefixId + (this.option?.key || '');
  }
}
