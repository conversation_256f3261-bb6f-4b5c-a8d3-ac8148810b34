import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { DynamicFormAction } from '../dynamic-form.model';

@Component({
  selector: 'lib-button-action',
  templateUrl: './button-action.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ButtonActionComponent {
  @Input() set value( action: DynamicFormAction | string | undefined ) {
    this.data = typeof action === 'string' ? { label: action } : action;
  }

  @Output() action = new EventEmitter();

  data?: DynamicFormAction;

  onClick( event: MouseEvent ) {
    event.preventDefault();
    this.action.emit();
  }
}
