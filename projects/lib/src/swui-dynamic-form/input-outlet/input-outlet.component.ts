import { Component, Inject, Injector, Input, Optional, TypeProvider } from '@angular/core';
import {
  InputOption,
  MatDynamicFormConfig,
  MatDynamicFormWidgetConfig,
  SWUI_DYNAMIC_FORM_CONFIG,
  SWUI_DYNAMIC_FORM_WIDGET_CONFIG
} from '../dynamic-form.model';
import { FormControl } from '@angular/forms';

interface Widgets {
  [type: string]: TypeProvider;
}

@Component({
  selector: 'lib-input-outlet',
  template: `
    <ng-container *ngIf="componentInjector && component">
      <ng-container *ngComponentOutlet="component; injector: componentInjector"></ng-container>
    </ng-container>
  `
})
export class InputOutletComponent {
  @Input() type?: string;
  @Input() control?: FormControl;
  @Input() id?: string;
  @Input() option?: InputOption;
  @Input() readonly = false;
  @Input() submitted = false;

  private readonly widgets: Widgets;

  constructor( private readonly injector: Injector, @Inject(SWUI_DYNAMIC_FORM_CONFIG) @Optional() config?: MatDynamicFormConfig ) {
    this.widgets = (config?.widgets || []).reduce(( result, { type, component } ) => ({
      ...result,
      [type]: component
    }), {});
  }

  get component(): TypeProvider | undefined {
    return this.widgets[this.type || ''];
  }

  get componentInjector(): Injector | undefined {
    const config: MatDynamicFormWidgetConfig = {
      control: this.control,
      id: this.id,
      option: this.option,
      readonly: this.readonly,
      submitted: this.submitted
    };
    return this.option && Injector.create({
      providers: [{
        provide: SWUI_DYNAMIC_FORM_WIDGET_CONFIG,
        useValue: config
      }], parent: this.injector
    });
  }
}
