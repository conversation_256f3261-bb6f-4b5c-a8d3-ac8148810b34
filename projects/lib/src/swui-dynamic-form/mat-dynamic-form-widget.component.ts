import { FormControl } from '@angular/forms';
import { MatDynamicFormWidgetConfig } from './dynamic-form.model';

export abstract class MatDynamicFormWidgetComponent<T> {
  readonly id?: string;
  readonly control?: FormControl;
  readonly option?: T;
  readonly submitted: boolean;
  readonly readonly: boolean;

  protected constructor( { control, id, option, readonly, submitted }: MatDynamicFormWidgetConfig ) {
    this.id = id;
    this.control = control;
    this.option = option;
    this.readonly = readonly;
    this.submitted = submitted;
  }
}
