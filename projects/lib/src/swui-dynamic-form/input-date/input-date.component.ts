import { Component, Input, OnDestroy, Optional } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SwuiDateTimepickerConfig } from '../../swui-datetimepicker/swui-datetimepicker.interface';
import { MatDynamicFormService } from '../mat-dynamic-form.service';
import { DateInputOption } from '../dynamic-form.model';

@Component({
  selector: 'lib-input-date',
  templateUrl: './input-date.component.html',
  styleUrls: ['./input-date.component.scss']
})
export class InputDateComponent implements OnDestroy {
  @Input() control?: FormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: DateInputOption | undefined ) {
    this.title = value?.title;
    this.clearable = value?.clearable;
    if (value?.config) {
      Object.assign(this.config, value.config);
    }
  }

  title?: string;
  clearable?: boolean;
  config: SwuiDateTimepickerConfig = {};
  private readonly destroy$ = new Subject<void>();
  constructor( @Optional() service: MatDynamicFormService ) {
    if (service) {
      service.timezone
        .pipe(takeUntil(this.destroy$))
        .subscribe(timeZone => {
          this.config.timeZone = timeZone;
        });
    }
  }

  clear( event: MouseEvent ) {
    event.stopPropagation();
    if (this.control) {
      this.control.setValue(null);
    }
  }

  isEmpty() {
    const value = this.control?.value;
    return !value;
  }


  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
