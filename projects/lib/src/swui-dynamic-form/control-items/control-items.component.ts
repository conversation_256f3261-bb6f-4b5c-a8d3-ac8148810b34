import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ControlItem, DynamicFormAction } from '../dynamic-form.model';

@Component({
  selector: 'lib-control-items',
  templateUrl: './control-items.component.html',
  styleUrls: ['./control-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ControlItemsComponent {
  @Input() items: ControlItem[] = [];
  @Input() prefixId = '';
  @Input() readonly = false;
  @Input() submitted = false;

  actionAdd( action: DynamicFormAction | string | undefined ): DynamicFormAction | string {
    return action || {
      label: 'LOBBY.THEMES.btnAdd',
      class: 'pull-right ml-20'
    };
  }

  actionRemove( action: DynamicFormAction | string | undefined ): DynamicFormAction | string {
    return action || {
      fontIcon: {
        fontSet: 'icomoon',
        name: 'icon-trash'
      }
    };
  }
}
