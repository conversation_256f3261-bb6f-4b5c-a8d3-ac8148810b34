import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { TextareaInputOption, TextInputOption } from '../dynamic-form.model';

function parseAutosize( value: TextareaInputOption['autosize'] ): InputTextComponent['autosize'] {
  if (value) {
    return typeof value === 'boolean' ? {} : value;
  }
  return undefined;
}

@Component({
  selector: 'lib-input-text',
  templateUrl: './input-text.component.html',
  styleUrls: ['./input-text.component.scss']
})
export class InputTextComponent {
  @Input() control?: FormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: TextInputOption | TextareaInputOption | undefined ) {
    this.type = value?.type || 'text';
    this.title = value?.title;
    this.clearable = value?.clearable;
    this.autosize = parseAutosize((value as TextareaInputOption)?.autosize);
    this.required = value?.required;
    this.errorMessages = value?.validation?.messages;
  }

  type = 'text';
  title?: string;
  clearable?: boolean;
  autosize?: {
    minRows?: number;
    maxRows?: number;
  };
  required?: boolean;
  errorMessages?: { [key: string]: string };


  clear( event: MouseEvent ) {
    event.stopPropagation();
    if (this.control) {
      this.control.setValue('');
    }
  }
}
