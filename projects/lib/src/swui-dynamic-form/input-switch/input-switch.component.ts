import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SwitchInputOption } from '../dynamic-form.model';

@Component({
  selector: 'lib-input-switch',
  templateUrl: './input-switch.component.html',
  styleUrls: ['./input-switch.component.scss']
})
export class InputSwitchComponent {
  @Input() control?: FormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: SwitchInputOption | undefined ) {
    this.title = value?.title || '';
    this.errorMessages = value?.validation?.messages;
  }

  title = '';
  errorMessages?: { [key: string]: string };
}
