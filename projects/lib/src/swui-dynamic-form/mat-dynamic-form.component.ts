import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input, OnChanges, SimpleChanges
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ControlItem, ControlOption, createControlItem, DynamicFormOptionData } from './dynamic-form.model';

@Component({
  selector: 'lib-dynamic-form',
  templateUrl: './mat-dynamic-form.component.html',
  styleUrls: [
    './mat-dynamic-form.component.scss'
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MatDynamicFormComponent implements OnChanges {
  @Input() options?: DynamicFormOptionData | ControlOption[];
  @Input() controlName?: string;
  @Input() form = new FormGroup({});
  @Input() readonly = false;
  @Input() submitted = false;
  @Input() @HostBinding('style.flex-direction') direction = 'column';

  items: ControlItem[] = [];

  constructor( private cdr: ChangeDetectorRef ) {
  }

  ngOnChanges( changes: SimpleChanges ): void {
    if (changes.hasOwnProperty('options')) {
      this.onOptions(changes.options.currentValue);
    }
  }

  private onOptions( value: DynamicFormOptionData | ControlOption[] ): void {
    const options: ControlOption[] = Array.isArray(value) ? value : Object.entries(value).map(( [key, option] ) => ({
      ...option,
      key,
    }));
    this.items = options.map(option => createControlItem(option));

    if (this.controlName) {
      const controls = this.items.reduce(( result, item ) => ({
        ...result,
        [item.option.key]: item.control
      }), {});
      this.form.setControl(this.controlName, new FormGroup(controls));
    } else {
      this.items.forEach(item => {
        this.form.setControl(item.option.key, item.control);
      });
      const keys = this.items.map(( { option: { key } } ) => key);
      Object.keys(this.form.controls).forEach(control => {
        if (!keys.includes(control)) {
          this.form.removeControl(control);
        }
      });
    }

    this.cdr.detectChanges();
  }
}
